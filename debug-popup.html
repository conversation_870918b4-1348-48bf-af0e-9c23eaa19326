<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Smart Form Filler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background: #4facfe;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #3a8bfe;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 تشخيص Smart Form Filler</h1>
    
    <div class="debug-section">
        <h3>1. اختبار رفع الملفات</h3>
        <input type="file" id="test-file-input" accept=".csv,.json">
        <button onclick="testFileUpload()">اختبار رفع الملف</button>
        <div id="file-log" class="log"></div>
    </div>
    
    <div class="debug-section">
        <h3>2. اختبار معالجة CSV</h3>
        <button onclick="testCSVParsing()">اختبار معالجة CSV</button>
        <div id="csv-log" class="log"></div>
    </div>
    
    <div class="debug-section">
        <h3>3. اختبار معالجة JSON</h3>
        <button onclick="testJSONParsing()">اختبار معالجة JSON</button>
        <div id="json-log" class="log"></div>
    </div>
    
    <div class="debug-section">
        <h3>4. اختبار التخزين</h3>
        <button onclick="testStorage()">اختبار التخزين</button>
        <button onclick="clearStorage()">مسح التخزين</button>
        <div id="storage-log" class="log"></div>
    </div>
    
    <div class="debug-section">
        <h3>5. سجل الأخطاء</h3>
        <button onclick="clearErrorLog()">مسح السجل</button>
        <div id="error-log" class="log"></div>
    </div>

    <script src="utils.js"></script>
    <script>
        // Global error handler
        window.addEventListener('error', function(e) {
            logError('Global Error: ' + e.message + ' at ' + e.filename + ':' + e.lineno);
        });

        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function logError(message) {
            log('error-log', message);
            console.error(message);
        }

        function clearErrorLog() {
            document.getElementById('error-log').textContent = '';
        }

        function testFileUpload() {
            const fileInput = document.getElementById('test-file-input');
            const file = fileInput.files[0];
            
            log('file-log', 'بدء اختبار رفع الملف...');
            
            if (!file) {
                log('file-log', 'خطأ: لم يتم اختيار ملف');
                return;
            }
            
            log('file-log', `ملف مختار: ${file.name}`);
            log('file-log', `حجم الملف: ${file.size} بايت`);
            log('file-log', `نوع الملف: ${file.type}`);
            
            // Test file reading
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const text = e.target.result;
                log('file-log', `تم قراءة الملف بنجاح. طول النص: ${text.length}`);
                log('file-log', `أول 100 حرف: ${text.substring(0, 100)}`);
                
                // Test parsing
                try {
                    let result;
                    if (file.name.endsWith('.csv')) {
                        result = SmartFormUtils.parseCSV(text);
                        log('file-log', `تم معالجة CSV: ${result.headers.length} أعمدة، ${result.data.length} صفوف`);
                    } else if (file.name.endsWith('.json')) {
                        result = SmartFormUtils.parseJSON(text);
                        log('file-log', `تم معالجة JSON: ${result.headers.length} أعمدة، ${result.data.length} صفوف`);
                    }
                    log('file-log', 'تم الاختبار بنجاح! ✅');
                } catch (error) {
                    log('file-log', `خطأ في المعالجة: ${error.message}`);
                    logError(`File parsing error: ${error.message}`);
                }
            };
            
            reader.onerror = function() {
                log('file-log', 'خطأ في قراءة الملف');
                logError('File reading error');
            };
            
            reader.readAsText(file, 'UTF-8');
        }

        function testCSVParsing() {
            log('csv-log', 'بدء اختبار معالجة CSV...');
            
            const testCSV = `الاسم,العمر,المدينة
أحمد,25,الرياض
فاطمة,30,جدة
محمد,28,الدمام`;
            
            try {
                const result = SmartFormUtils.parseCSV(testCSV);
                log('csv-log', `نجح الاختبار! الأعمدة: ${result.headers.join(', ')}`);
                log('csv-log', `عدد الصفوف: ${result.data.length}`);
                log('csv-log', `أول صف: ${JSON.stringify(result.data[0])}`);
                log('csv-log', 'اختبار CSV مكتمل ✅');
            } catch (error) {
                log('csv-log', `خطأ: ${error.message}`);
                logError(`CSV parsing error: ${error.message}`);
            }
        }

        function testJSONParsing() {
            log('json-log', 'بدء اختبار معالجة JSON...');
            
            const testJSON = `[
                {"الاسم": "أحمد", "العمر": "25", "المدينة": "الرياض"},
                {"الاسم": "فاطمة", "العمر": "30", "المدينة": "جدة"}
            ]`;
            
            try {
                const result = SmartFormUtils.parseJSON(testJSON);
                log('json-log', `نجح الاختبار! الأعمدة: ${result.headers.join(', ')}`);
                log('json-log', `عدد الصفوف: ${result.data.length}`);
                log('json-log', `أول صف: ${JSON.stringify(result.data[0])}`);
                log('json-log', 'اختبار JSON مكتمل ✅');
            } catch (error) {
                log('json-log', `خطأ: ${error.message}`);
                logError(`JSON parsing error: ${error.message}`);
            }
        }

        async function testStorage() {
            log('storage-log', 'بدء اختبار التخزين...');
            
            try {
                // Test data
                const testData = {
                    test: 'بيانات تجريبية',
                    timestamp: new Date().toISOString()
                };
                
                // Save to storage
                await chrome.storage.local.set({ testData });
                log('storage-log', 'تم حفظ البيانات التجريبية');
                
                // Read from storage
                const result = await chrome.storage.local.get(['testData']);
                if (result.testData) {
                    log('storage-log', `تم قراءة البيانات: ${JSON.stringify(result.testData)}`);
                    log('storage-log', 'اختبار التخزين مكتمل ✅');
                } else {
                    log('storage-log', 'خطأ: لم يتم العثور على البيانات المحفوظة');
                }
                
            } catch (error) {
                log('storage-log', `خطأ في التخزين: ${error.message}`);
                logError(`Storage error: ${error.message}`);
            }
        }

        async function clearStorage() {
            try {
                await chrome.storage.local.clear();
                log('storage-log', 'تم مسح جميع البيانات المخزنة');
            } catch (error) {
                log('storage-log', `خطأ في مسح التخزين: ${error.message}`);
                logError(`Storage clear error: ${error.message}`);
            }
        }

        // Initialize
        log('error-log', 'تم تحميل صفحة التشخيص');
    </script>
</body>
</html>
