<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج تجريبي - Smart Form Filler</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .radio-item input[type="radio"] {
            width: auto;
            margin: 0;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #4facfe;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .info-box p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>نموذج التسجيل التجريبي</h1>
            <p>استخدم هذا النموذج لاختبار إضافة Smart Form Filler</p>
        </div>
        
        <div class="form-container">
            <div class="info-box">
                <h3>🎯 تعليمات الاختبار</h3>
                <p>
                    1. ارفع ملف البيانات في الإضافة<br>
                    2. فعل وضع التعليم<br>
                    3. انقر بالزر الأيمن على الحقول لربطها<br>
                    4. احفظ النمط وجرب التعبئة التلقائية
                </p>
            </div>
            
            <form id="test-form" action="#" method="post">
                <!-- معلومات شخصية -->
                <h3 style="color: #333; border-bottom: 2px solid #4facfe; padding-bottom: 10px;">المعلومات الشخصية</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="first-name">الاسم الأول <span class="required">*</span></label>
                        <input type="text" id="first-name" name="first_name" required>
                    </div>
                    <div class="form-group">
                        <label for="last-name">الاسم الأخير <span class="required">*</span></label>
                        <input type="text" id="last-name" name="last_name" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="age">العمر</label>
                        <input type="number" id="age" name="age" min="18" max="100">
                    </div>
                </div>
                
                <!-- الموقع -->
                <h3 style="color: #333; border-bottom: 2px solid #4facfe; padding-bottom: 10px; margin-top: 30px;">معلومات الموقع</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="country">البلد</label>
                        <select id="country" name="country">
                            <option value="">اختر البلد</option>
                            <option value="السعودية">السعودية</option>
                            <option value="الإمارات">الإمارات</option>
                            <option value="الكويت">الكويت</option>
                            <option value="قطر">قطر</option>
                            <option value="البحرين">البحرين</option>
                            <option value="عمان">عمان</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="city">المدينة</label>
                        <input type="text" id="city" name="city">
                    </div>
                </div>
                
                <!-- معلومات مهنية -->
                <h3 style="color: #333; border-bottom: 2px solid #4facfe; padding-bottom: 10px; margin-top: 30px;">المعلومات المهنية</h3>
                
                <div class="form-group">
                    <label for="profession">المهنة</label>
                    <input type="text" id="profession" name="profession">
                </div>
                
                <div class="form-group">
                    <label for="salary">الراتب المطلوب</label>
                    <input type="number" id="salary" name="salary" min="0">
                </div>
                
                <!-- الجنس -->
                <div class="form-group">
                    <label>الجنس</label>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="male" name="gender" value="ذكر">
                            <label for="male">ذكر</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="female" name="gender" value="أنثى">
                            <label for="female">أنثى</label>
                        </div>
                    </div>
                </div>
                
                <!-- خيارات إضافية -->
                <div class="checkbox-group">
                    <input type="checkbox" id="newsletter" name="newsletter" value="1">
                    <label for="newsletter">أرغب في تلقي النشرة الإخبارية</label>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="terms" name="terms" value="1" required>
                    <label for="terms">أوافق على الشروط والأحكام <span class="required">*</span></label>
                </div>
                
                <!-- ملاحظات -->
                <div class="form-group">
                    <label for="notes">ملاحظات إضافية</label>
                    <textarea id="notes" name="notes" rows="4" placeholder="اكتب أي ملاحظات إضافية هنا..."></textarea>
                </div>
                
                <!-- زر الإرسال -->
                <button type="submit" class="submit-btn" id="submit-btn">
                    إرسال النموذج
                </button>
            </form>
        </div>
    </div>
    
    <script>
        // معالج إرسال النموذج
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // جمع البيانات
            const formData = new FormData(this);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // عرض البيانات
            alert('تم إرسال النموذج بنجاح!\n\nالبيانات المرسلة:\n' + JSON.stringify(data, null, 2));
            
            console.log('Form submitted with data:', data);
        });
        
        // إضافة تأثيرات بصرية
        document.querySelectorAll('input, select, textarea').forEach(element => {
            element.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            element.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
