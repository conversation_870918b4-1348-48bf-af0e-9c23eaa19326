# Smart Form Filler - دليل المطور

## نظرة عامة
Smart Form Filler هي إضافة متصفح ذكية تسمح للمستخدمين بتعبئة النماذج تلقائياً باستخدام بيانات مخزنة مسبقاً. تدعم الإضافة ملفات CSV و JSON وتوفر واجهة سهلة الاستخدام لربط حقول النماذج بأعمدة البيانات.

## الميزات الرئيسية
- ✅ رفع ومعالجة ملفات CSV و JSON
- ✅ وضع التعليم لربط الحقول بالبيانات
- ✅ قائمة سياق (Right-click) لربط الحقول
- ✅ دعم القيم الثابتة والمتغيرة
- ✅ التعبئة التلقائية مع إعدادات مدة الانتظار
- ✅ حفظ وإدارة أنماط متعددة
- ✅ واجهة مستخدم عربية بالكامل
- ✅ دعم أنواع مختلفة من الحقول

## هيكل المشروع

```
smart-form-filler/
├── manifest.json          # تكوين الإضافة
├── popup.html             # واجهة المستخدم الرئيسية
├── popup.js               # منطق واجهة المستخدم
├── content.js             # التفاعل مع صفحات الويب
├── background.js          # إدارة الخلفية والبيانات
├── utils.js               # وظائف مساعدة
├── styles.css             # تنسيق واجهة المستخدم
├── content-styles.css     # تنسيق المحتوى
├── icons/                 # أيقونات الإضافة
├── README.md              # الوثائق الأساسية
├── README_USER_GUIDE.md   # دليل المستخدم
└── README_DEVELOPER.md    # دليل المطور
```

## متطلبات التطوير
- متصفح Chrome أو Firefox أو Edge
- محرر نصوص (VS Code مُوصى به)
- معرفة أساسية بـ JavaScript و HTML و CSS

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd smart-form-filler
```

### 2. تثبيت الإضافة في Chrome
1. افتح Chrome واذهب إلى `chrome://extensions/`
2. فعل "وضع المطور" (Developer mode)
3. انقر على "تحميل إضافة غير مُعبأة" (Load unpacked)
4. اختر مجلد المشروع
5. ستظهر الإضافة في شريط الأدوات

### 3. تثبيت الإضافة في Firefox
1. افتح Firefox واذهب إلى `about:debugging`
2. انقر على "This Firefox"
3. انقر على "Load Temporary Add-on"
4. اختر ملف `manifest.json`

## الاختبار

### اختبار أساسي
1. افتح موقع يحتوي على نموذج (مثل نموذج تسجيل)
2. انقر على أيقونة الإضافة
3. ارفع ملف CSV تجريبي
4. فعل وضع التعليم
5. انقر بالزر الأيمن على الحقول لربطها
6. احفظ النمط
7. جرب التعبئة التلقائية

### ملف CSV تجريبي
```csv
الاسم_الأول,الاسم_الأخير,البريد,الهاتف,البلد
أحمد,محمد,<EMAIL>,0501234567,السعودية
فاطمة,علي,<EMAIL>,0507654321,السعودية
محمد,أحمد,<EMAIL>,0509876543,السعودية
```

## الملفات الرئيسية

### manifest.json
يحدد تكوين الإضافة والصلاحيات المطلوبة:
- `permissions`: الصلاحيات المطلوبة
- `content_scripts`: الملفات التي تُحقن في صفحات الويب
- `background`: ملف الخلفية
- `action`: تكوين النافذة المنبثقة

### popup.js
يحتوي على منطق واجهة المستخدم:
- `SmartFormPopup`: الكلاس الرئيسي
- معالجة رفع الملفات
- إدارة الأنماط
- التحكم في التعبئة التلقائية

### content.js
يتعامل مع صفحات الويب:
- `SmartFormContent`: الكلاس الرئيسي
- وضع التعليم
- قائمة السياق
- التعبئة التلقائية

### background.js
يدير العمليات الخلفية:
- `SmartFormBackground`: الكلاس الرئيسي
- إدارة التخزين
- قوائم السياق
- الإشعارات

### utils.js
يحتوي على وظائف مساعدة:
- `SmartFormUtils`: كلاس الوظائف المساعدة
- معالجة CSV و JSON
- التفاعل مع DOM
- التحقق من صحة البيانات

## API الداخلي

### التخزين
```javascript
// حفظ البيانات
chrome.storage.local.set({ key: value });

// قراءة البيانات
chrome.storage.local.get(['key']);

// حذف البيانات
chrome.storage.local.remove(['key']);
```

### الرسائل بين المكونات
```javascript
// إرسال رسالة
chrome.runtime.sendMessage({
    action: 'actionName',
    data: data
});

// استقبال رسالة
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    // معالجة الرسالة
});
```

### التفاعل مع DOM
```javascript
// الحصول على حقول النموذج
const fields = SmartFormUtils.getFormFields();

// محاكاة إدخال المستخدم
SmartFormUtils.simulateInput(element, value);

// انتظار ظهور عنصر
await SmartFormUtils.waitForElement(selector);
```

## التخصيص والتطوير

### إضافة نوع ملف جديد
1. أضف معالج في `utils.js`:
```javascript
static parseXML(xmlText) {
    // منطق معالجة XML
}
```

2. أضف الدعم في `popup.js`:
```javascript
if (file.name.endsWith('.xml')) {
    parsedData = SmartFormUtils.parseXML(text);
}
```

### إضافة نوع حقل جديد
1. أضف الدعم في `content.js`:
```javascript
fillField(element, value) {
    if (element.type === 'date') {
        // منطق تعبئة حقل التاريخ
    }
}
```

### تخصيص الواجهة
1. عدل `styles.css` لتغيير الألوان والتخطيط
2. أضف CSS جديد في `content-styles.css` للتأثيرات

## الأمان والخصوصية
- جميع البيانات تُخزن محلياً في المتصفح
- لا يتم إرسال أي بيانات لخوادم خارجية
- الإضافة تطلب الحد الأدنى من الصلاحيات
- البيانات الحساسة تُشفر قبل التخزين

## استكشاف الأخطاء

### مشاكل شائعة
1. **الإضافة لا تظهر**: تأكد من تفعيل وضع المطور
2. **لا تعمل في صفحة معينة**: تحقق من console للأخطاء
3. **البيانات لا تُحفظ**: تحقق من صلاحيات التخزين

### أدوات التطوير
```javascript
// تفعيل وضع التطوير
console.log('Smart Form Filler Debug Mode');

// عرض البيانات المخزنة
chrome.storage.local.get(null, console.log);

// مسح جميع البيانات
chrome.storage.local.clear();
```

## المساهمة
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التعليقات
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم
- للمشاكل التقنية: افتح Issue في GitHub
- للاقتراحات: استخدم Discussions
- للتوثيق: راجع Wiki

## خارطة الطريق
- [ ] دعم ملفات Excel المتقدمة
- [ ] تصدير/استيراد الأنماط
- [ ] واجهة إعدادات متقدمة
- [ ] دعم التعبئة المتسلسلة
- [ ] تكامل مع APIs خارجية
- [ ] دعم المزيد من اللغات
