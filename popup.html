<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Form Filler</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Smart Form Filler</h1>
            <p>أداة ذكية لتعبئة النماذج تلقائياً</p>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- File Upload Section -->
            <div class="section" id="upload-section">
                <h2>📁 رفع ملف البيانات</h2>
                <div class="upload-area" id="upload-area">
                    <div class="upload-icon">📄</div>
                    <p>اسحب وأفلت ملف البيانات هنا</p>
                    <p class="file-types"><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Excel مدعوم</p>
                    <input type="file" id="file-input" accept=".csv,.json,.xlsx,.xls" hidden>
                    <button class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                        اختر ملف
                    </button>
                </div>
                <div class="file-info" id="file-info" style="display: none;">
                    <div class="file-details">
                        <span class="file-name" id="file-name"></span>
                        <span class="file-size" id="file-size"></span>
                    </div>
                    <button class="btn btn-secondary btn-small" id="remove-file">إزالة</button>
                </div>
            </div>

            <!-- Data Preview Section -->
            <div class="section" id="preview-section" style="display: none;">
                <h2>👁️ معاينة البيانات</h2>
                <div class="data-preview" id="data-preview">
                    <table id="data-table">
                        <thead id="table-head"></thead>
                        <tbody id="table-body"></tbody>
                    </table>
                </div>
                <div class="data-stats">
                    <span>عدد الصفوف: <span id="rows-count">0</span></span>
                    <span>عدد الأعمدة: <span id="columns-count">0</span></span>
                </div>
            </div>

            <!-- Pattern Management Section -->
            <div class="section" id="patterns-section">
                <h2>🎯 إدارة الأنماط</h2>
                
                <!-- Teaching Mode Toggle -->
                <div class="mode-toggle">
                    <label class="toggle-switch">
                        <input type="checkbox" id="teaching-mode">
                        <span class="slider"></span>
                    </label>
                    <span class="mode-label">وضع التعليم</span>
                    <div class="mode-description">
                        فعل هذا الوضع لإنشاء نمط جديد أو تعديل نمط موجود
                    </div>
                </div>

                <!-- Current Pattern Info -->
                <div class="current-pattern" id="current-pattern" style="display: none;">
                    <h3>النمط الحالي</h3>
                    <div class="pattern-info">
                        <div class="pattern-name" id="pattern-name">غير محدد</div>
                        <div class="pattern-url" id="pattern-url"></div>
                        <div class="pattern-fields" id="pattern-fields">0 حقل مربوط</div>
                    </div>
                    <div class="pattern-actions">
                        <button class="btn btn-primary" id="save-pattern">حفظ النمط</button>
                        <button class="btn btn-secondary" id="clear-pattern">مسح النمط</button>
                    </div>
                </div>

                <!-- Saved Patterns List -->
                <div class="saved-patterns">
                    <h3>الأنماط المحفوظة</h3>
                    <div class="patterns-list" id="patterns-list">
                        <div class="no-patterns">لا توجد أنماط محفوظة</div>
                    </div>
                </div>
            </div>

            <!-- Smart Detection Section -->
            <div class="section" id="smart-detection-section">
                <h2>🤖 التعرف الذكي</h2>

                <!-- Auto Detection Toggle -->
                <div class="mode-toggle">
                    <label class="toggle-switch">
                        <input type="checkbox" id="auto-detection-mode">
                        <span class="slider"></span>
                    </label>
                    <span class="mode-label">التعرف التلقائي</span>
                    <div class="mode-description">
                        تعرف تلقائي على الأزرار والقوائم المنسدلة وأزرار الراديو
                    </div>
                </div>

                <!-- Detection Results -->
                <div class="detection-results" id="detection-results" style="display: none;">
                    <h3>العناصر المكتشفة</h3>
                    <div class="detected-elements">
                        <div class="element-type">
                            <span class="type-icon">🔘</span>
                            <span class="type-name">أزرار الإنشاء:</span>
                            <span class="type-count" id="create-buttons-count">0</span>
                        </div>
                        <div class="element-type">
                            <span class="type-icon">📋</span>
                            <span class="type-name">القوائم المنسدلة:</span>
                            <span class="type-count" id="dropdowns-count">0</span>
                        </div>
                        <div class="element-type">
                            <span class="type-icon">🔘</span>
                            <span class="type-name">أزرار الراديو:</span>
                            <span class="type-count" id="radio-buttons-count">0</span>
                        </div>
                        <div class="element-type">
                            <span class="type-icon">🪟</span>
                            <span class="type-name">النوافذ المنبثقة:</span>
                            <span class="type-count" id="modals-count">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recording Section -->
            <div class="section" id="recording-section">
                <h2>📹 تسجيل الخطوات</h2>

                <!-- Recording Controls -->
                <div class="recording-controls">
                    <div class="recording-status" id="recording-status">
                        <span class="status-indicator" id="status-indicator">⚪</span>
                        <span class="status-text" id="status-text">غير مسجل</span>
                    </div>

                    <div class="recording-buttons">
                        <button class="btn btn-success" id="start-recording">
                            <span class="btn-icon">🔴</span>
                            بدء التسجيل
                        </button>
                        <button class="btn btn-warning" id="stop-recording" disabled>
                            <span class="btn-icon">⏹️</span>
                            إيقاف التسجيل
                        </button>
                        <button class="btn btn-info" id="play-recording" disabled>
                            <span class="btn-icon">▶️</span>
                            تشغيل التسجيل
                        </button>
                    </div>
                </div>

                <!-- Recorded Steps -->
                <div class="recorded-steps" id="recorded-steps" style="display: none;">
                    <h3>الخطوات المسجلة</h3>
                    <div class="steps-count">
                        عدد الخطوات: <span id="steps-count">0</span>
                    </div>
                    <div class="steps-list" id="steps-list"></div>

                    <!-- Recording Actions -->
                    <div class="recording-actions" style="margin-top: 15px;">
                        <button class="btn btn-success" id="save-recording">
                            <span class="btn-icon">💾</span>
                            حفظ التسجيل
                        </button>
                        <button class="btn btn-secondary" id="export-recording">
                            <span class="btn-icon">📤</span>
                            تصدير
                        </button>
                        <button class="btn btn-danger" id="clear-recording">
                            <span class="btn-icon">🗑️</span>
                            مسح
                        </button>
                    </div>
                </div>
            </div>

            <!-- Saved Recordings Section -->
            <div class="section" id="saved-recordings-section">
                <h2>💾 التسجيلات المحفوظة</h2>

                <div class="recordings-list" id="recordings-list">
                    <div class="no-recordings" style="text-align: center; color: #666; padding: 20px;">
                        لا توجد تسجيلات محفوظة
                    </div>
                </div>

                <div class="recordings-actions" style="margin-top: 15px;">
                    <button class="btn btn-primary" id="import-recording">
                        <span class="btn-icon">📥</span>
                        استيراد تسجيل
                    </button>
                    <button class="btn btn-secondary" id="export-all-recordings">
                        <span class="btn-icon">📦</span>
                        تصدير الكل
                    </button>
                </div>
            </div>

            <!-- Auto Fill Section -->
            <div class="section" id="autofill-section" style="display: none;">
                <h2>⚡ التعبئة التلقائية</h2>
                <div class="autofill-controls">
                    <div class="row-selector">
                        <label for="data-row">اختر صف البيانات:</label>
                        <select id="data-row">
                            <option value="">-- اختر صف --</option>
                        </select>
                    </div>
                    <div class="wait-time">
                        <label for="wait-time">مدة الانتظار (ثانية):</label>
                        <input type="number" id="wait-time" min="1" max="10" value="3">
                    </div>
                    <button class="btn btn-success" id="start-autofill">بدء التعبئة التلقائية</button>
                </div>
                <div class="autofill-progress" id="autofill-progress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">جاري التعبئة...</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="status" id="status">جاهز</div>
            <div class="version">الإصدار 1.0.0</div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="utils.js"></script>
    <script src="popup.js"></script>
</body>
</html>
