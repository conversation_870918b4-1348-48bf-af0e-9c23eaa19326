<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار وضع التعليم - Smart Form Filler</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .form-row {
            display: flex;
            gap: 20px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #4facfe;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .info-box h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #333;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="status">جاهز للاختبار</div>
    
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار وضع التعليم</h1>
            <p>استخدم هذه الصفحة لاختبار جميع وظائف Smart Form Filler</p>
        </div>
        
        <div class="content">
            <div class="info-box">
                <h4>📋 تعليمات الاختبار</h4>
                <ol>
                    <li>ارفع ملف البيانات في الإضافة أولاً</li>
                    <li>فعل وضع التعليم من الإضافة</li>
                    <li>انقر بالزر الأيمن على أي عنصر أدناه</li>
                    <li>يجب أن تظهر قائمة بأعمدة البيانات</li>
                    <li>اختبر جميع أنواع العناصر المختلفة</li>
                </ol>
            </div>
            
            <!-- نموذج شامل للاختبار -->
            <form id="comprehensive-test-form">
                <div class="test-section">
                    <h3>🔤 حقول النص</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-name">اسم المنتج *</label>
                            <input type="text" id="product-name" name="product_name" placeholder="أدخل اسم المنتج" required>
                        </div>
                        <div class="form-group">
                            <label for="product-sku">رمز المنتج (SKU)</label>
                            <input type="text" id="product-sku" name="product_sku" placeholder="SKU-001">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-price">السعر</label>
                            <input type="number" id="product-price" name="product_price" placeholder="0.00" step="0.01">
                        </div>
                        <div class="form-group">
                            <label for="product-quantity">الكمية</label>
                            <input type="number" id="product-quantity" name="product_quantity" placeholder="1" min="1">
                        </div>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>📧 حقول الاتصال</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="contact-email">البريد الإلكتروني</label>
                            <input type="email" id="contact-email" name="contact_email" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="contact-phone">رقم الهاتف</label>
                            <input type="tel" id="contact-phone" name="contact_phone" placeholder="+966501234567">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="contact-website">الموقع الإلكتروني</label>
                        <input type="url" id="contact-website" name="contact_website" placeholder="https://example.com">
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>📅 حقول التاريخ والوقت</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="start-date">تاريخ البداية</label>
                            <input type="date" id="start-date" name="start_date">
                        </div>
                        <div class="form-group">
                            <label for="start-time">وقت البداية</label>
                            <input type="time" id="start-time" name="start_time">
                        </div>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>📋 القوائم المنسدلة</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="category">الفئة</label>
                            <select id="category" name="category">
                                <option value="">اختر الفئة</option>
                                <option value="electronics">إلكترونيات</option>
                                <option value="clothing">ملابس</option>
                                <option value="books">كتب</option>
                                <option value="home">منزل وحديقة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="unit">الوحدة</label>
                            <select id="unit" name="unit">
                                <option value="">اختر الوحدة</option>
                                <option value="piece">قطعة</option>
                                <option value="kg">كيلوجرام</option>
                                <option value="liter">لتر</option>
                                <option value="meter">متر</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>☑️ خيارات الاختيار</h3>
                    <div class="form-group">
                        <label>حالة المنتج</label>
                        <div class="radio-group">
                            <div class="radio-item">
                                <input type="radio" id="status-active" name="status" value="active">
                                <label for="status-active">نشط</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="status-inactive" name="status" value="inactive">
                                <label for="status-inactive">غير نشط</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="status-draft" name="status" value="draft">
                                <label for="status-draft">مسودة</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="featured" name="featured" value="1">
                        <label for="featured">منتج مميز</label>
                    </div>
                    
                    <div class="checkbox-group">
                        <input type="checkbox" id="on-sale" name="on_sale" value="1">
                        <label for="on-sale">في التخفيضات</label>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>📝 مناطق النص</h3>
                    <div class="form-group">
                        <label for="description">وصف المنتج</label>
                        <textarea id="description" name="description" rows="4" placeholder="أدخل وصف مفصل للمنتج..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">ملاحظات إضافية</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات أخرى..."></textarea>
                    </div>
                </div>
                
                <div class="test-section">
                    <h3>🔘 الأزرار والعمليات</h3>
                    <div class="button-group">
                        <button type="submit" class="btn btn-primary" id="create-btn">
                            إنشاء منتج
                        </button>
                        <button type="button" class="btn btn-secondary" id="save-draft-btn">
                            حفظ كمسودة
                        </button>
                        <button type="button" class="btn btn-success" id="preview-btn">
                            معاينة
                        </button>
                        <button type="reset" class="btn btn-danger" id="reset-btn">
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // تتبع حالة الاختبار
        let testStatus = {
            teachingMode: false,
            elementsHighlighted: 0,
            contextMenuShown: 0,
            fieldsLinked: 0
        };
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('Test Status:', message);
        }
        
        // مراقبة تغييرات DOM للتحقق من تفعيل وضع التعليم
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const element = mutation.target;
                    if (element.classList.contains('smart-form-highlightable')) {
                        testStatus.elementsHighlighted++;
                        updateStatus(`تم تمييز ${testStatus.elementsHighlighted} عنصر`);
                    }
                }
            });
        });
        
        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            attributeFilter: ['class']
        });
        
        // مراقبة النقر بالزر الأيمن
        document.addEventListener('contextmenu', (e) => {
            if (e.target.classList.contains('smart-form-highlightable')) {
                testStatus.contextMenuShown++;
                updateStatus(`تم عرض قائمة السياق ${testStatus.contextMenuShown} مرة`);
            }
        });
        
        // معالج إرسال النموذج
        document.getElementById('comprehensive-test-form').addEventListener('submit', (e) => {
            e.preventDefault();
            updateStatus('تم إرسال النموذج - اختبار مكتمل!');
            
            // جمع البيانات
            const formData = new FormData(e.target);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            console.log('Form Data:', data);
            alert('تم إرسال النموذج بنجاح!\n\nراجع Console للتفاصيل.');
        });
        
        // معالجات الأزرار الأخرى
        document.getElementById('save-draft-btn').addEventListener('click', () => {
            updateStatus('تم حفظ المسودة');
        });
        
        document.getElementById('preview-btn').addEventListener('click', () => {
            updateStatus('تم فتح المعاينة');
        });
        
        document.getElementById('reset-btn').addEventListener('click', () => {
            updateStatus('تم إعادة تعيين النموذج');
        });
        
        // تحديث الحالة الأولية
        updateStatus('جاهز للاختبار - ارفع البيانات وفعل وضع التعليم');
        
        console.log('Test page loaded. Ready for Smart Form Filler testing.');
    </script>
</body>
</html>
