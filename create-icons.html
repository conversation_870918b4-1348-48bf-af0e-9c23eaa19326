<!DOCTYPE html>
<html>
<head>
    <title>Create Icons for Smart Form Filler</title>
</head>
<body>
    <h1>إنشاء أيقونات Smart Form Filler</h1>
    
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas32" width="32" height="32" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    
    <br><br>
    <button onclick="downloadIcons()">تحميل الأيقونات</button>
    
    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4facfe');
            gradient.addColorStop(1, '#00f2fe');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.2);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Add form icon
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('📝', size/2, size/2);
            
            return canvas;
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadIcons() {
            const canvas16 = createIcon('canvas16', 16);
            const canvas32 = createIcon('canvas32', 32);
            const canvas48 = createIcon('canvas48', 48);
            const canvas128 = createIcon('canvas128', 128);
            
            downloadCanvas(canvas16, 'icon16.png');
            setTimeout(() => downloadCanvas(canvas32, 'icon32.png'), 100);
            setTimeout(() => downloadCanvas(canvas48, 'icon48.png'), 200);
            setTimeout(() => downloadCanvas(canvas128, 'icon128.png'), 300);
        }
        
        // Create icons on load
        window.onload = function() {
            createIcon('canvas16', 16);
            createIcon('canvas32', 32);
            createIcon('canvas48', 48);
            createIcon('canvas128', 128);
        };
    </script>
</body>
</html>
