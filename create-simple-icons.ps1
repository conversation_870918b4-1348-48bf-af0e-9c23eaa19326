# PowerShell script to create simple icons for Smart Form Filler

Add-Type -AssemblyName System.Drawing

function Create-Icon {
    param(
        [int]$Size,
        [string]$OutputPath
    )
    
    # Create bitmap
    $bitmap = New-Object System.Drawing.Bitmap($Size, $Size)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Set high quality
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    
    # Create gradient brush
    $rect = New-Object System.Drawing.Rectangle(0, 0, $Size, $Size)
    $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush($rect, [System.Drawing.Color]::FromArgb(79, 172, 254), [System.Drawing.Color]::FromArgb(0, 242, 254), 45)
    
    # Fill background
    $graphics.FillRectangle($brush, $rect)
    
    # Create form rectangle
    $formSize = [int]($Size * 0.6)
    $formX = [int](($Size - $formSize) / 2)
    $formY = [int](($Size - $formSize) / 2)
    $formRect = New-Object System.Drawing.Rectangle($formX, $formY, $formSize, $formSize)
    
    $whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
    $graphics.FillRectangle($whiteBrush, $formRect)
    
    # Add form lines
    $blueBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(79, 172, 254))
    $lineHeight = [int]($Size * 0.03)
    $lineSpacing = [int]($Size * 0.08)
    $lineStartX = $formX + [int]($Size * 0.1)
    
    for ($i = 0; $i -lt 4; $i++) {
        $y = $formY + [int]($Size * 0.15) + ($i * $lineSpacing)
        $lineWidth = [int]($formSize * (0.5 + ($i * 0.1)))
        $lineRect = New-Object System.Drawing.Rectangle($lineStartX, $y, $lineWidth, $lineHeight)
        $graphics.FillRectangle($blueBrush, $lineRect)
    }
    
    # Save bitmap
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    $whiteBrush.Dispose()
    $blueBrush.Dispose()
    
    Write-Host "Created: $OutputPath"
}

# Create icons directory if it doesn't exist
if (!(Test-Path "icons")) {
    New-Item -ItemType Directory -Path "icons"
}

# Generate icons
$sizes = @(16, 32, 48, 128)

foreach ($size in $sizes) {
    $outputPath = "icons\icon$size.png"
    try {
        Create-Icon -Size $size -OutputPath $outputPath
    }
    catch {
        Write-Host "Error creating icon$size.png: $_"
    }
}

Write-Host "Icon generation complete!"
