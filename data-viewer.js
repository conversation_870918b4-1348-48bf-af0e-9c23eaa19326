// Smart Data Viewer - عارض البيانات الذكي

class SmartDataViewer {
    constructor() {
        this.data = [];
        this.columns = [];
        this.selectedFields = new Set();
        this.fieldMappings = {};
        this.currentRowIndex = 0;
        this.completedRows = new Set();
        this.isLearningMode = true; // وضع التعلم مفعل دائماً
        this.activeConnections = new Map(); // اتصالات نشطة مع التبويبات

        this.init();
    }
    
    init() {
        console.log('🚀 بدء تهيئة عارض البيانات...');

        this.loadDataFromStorage();
        this.setupEventListeners();
        this.loadSavedSettings();

        // تفعيل النظام المبسط
        setTimeout(() => {
            this.enableSimpleSystem();
        }, 1000);
    }

    // نظام مبسط للتتبع والتشغيل
    enableSimpleSystem() {
        console.log('🤖 تفعيل النظام المبسط...');

        // إظهار مؤشر بسيط
        this.showLearningIndicator();

        // تفعيل النظام في جميع التبويبات
        this.activateInAllTabs();

        console.log('✅ تم تفعيل النظام المبسط');
    }

    showLearningIndicator() {
        console.log('📊 إظهار مؤشر وضع التعلم...');

        // إزالة المؤشر السابق إن وجد
        const existing = document.getElementById('simple-learning-indicator');
        if (existing) existing.remove();

        // إنشاء مؤشر بسيط
        const indicator = document.createElement('div');
        indicator.id = 'simple-learning-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            direction: rtl;
        `;
        indicator.innerHTML = '🤖 النظام نشط - انقر بالزر الأيمن على الحقول';

        document.body.appendChild(indicator);
        console.log('✅ تم إظهار مؤشر وضع التعلم');
    }

    async activateInAllTabs() {
        console.log('📡 تفعيل النظام في جميع التبويبات...');

        try {
            const tabs = await chrome.tabs.query({});
            console.log(`📋 تم العثور على ${tabs.length} تبويب`);

            for (const tab of tabs) {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    try {
                        console.log(`📤 إرسال رسالة للتبويب: ${tab.id}`);

                        await chrome.tabs.sendMessage(tab.id, {
                            action: 'activateSimpleSystem',
                            dataViewerTabId: (await chrome.tabs.getCurrent()).id
                        });

                        console.log(`✅ تم تفعيل النظام في التبويب: ${tab.id}`);

                    } catch (error) {
                        console.log(`⚠️ لا يمكن الوصول للتبويب ${tab.id}:`, error.message);
                    }
                }
            }

        } catch (error) {
            console.error('❌ خطأ في تفعيل النظام:', error);
        }
    }

    async enableSmartSystem() {
        try {
            const currentTab = await chrome.tabs.getCurrent();
            this.dataViewerTabId = currentTab.id;

            // تفعيل النظام الذكي مع وضع التعلم في جميع التبويبات
            const tabs = await chrome.tabs.query({});

            for (const tab of tabs) {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    try {
                        await chrome.tabs.sendMessage(tab.id, {
                            action: 'enableSmartLearningSystem',
                            dataViewerTabId: this.dataViewerTabId,
                            learningMode: true,
                            currentData: {
                                data: this.data,
                                columns: this.columns,
                                currentRowIndex: this.currentRowIndex
                            }
                        });

                        // حفظ الاتصال النشط
                        this.activeConnections.set(tab.id, {
                            tabId: tab.id,
                            url: tab.url,
                            connected: true,
                            lastActivity: Date.now()
                        });

                    } catch (error) {
                        // تجاهل الأخطاء للتبويبات غير المتاحة
                    }
                }
            }

            // تحديث واجهة المستخدم
            this.updateLearningModeUI();
            this.showNotification('🤖 تم تفعيل وضع التعلم الذكي - انقر بالزر الأيمن على الحقول', 'success');

        } catch (error) {
            console.error('خطأ في تفعيل النظام الذكي:', error);
        }
    }

    updateLearningModeUI() {
        // إضافة مؤشر وضع التعلم
        const learningIndicator = document.createElement('div');
        learningIndicator.id = 'learning-mode-indicator';
        learningIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
            animation: pulse 2s infinite;
            direction: rtl;
        `;

        learningIndicator.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 16px;">🤖</span>
                <span>وضع التعلم نشط</span>
                <span style="font-size: 12px; opacity: 0.8;">الصف: ${this.currentRowIndex + 1}</span>
            </div>
        `;

        // إضافة الأنيميشن
        if (!document.getElementById('learning-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'learning-animation-styles';
            style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            `;
            document.head.appendChild(style);
        }

        // إزالة المؤشر السابق إن وجد
        const existingIndicator = document.getElementById('learning-mode-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        document.body.appendChild(learningIndicator);
    }
    
    async loadDataFromStorage() {
        try {
            const result = await chrome.storage.local.get(['currentData']);
            if (result.currentData) {
                this.data = result.currentData.data || [];
                this.columns = result.currentData.columns || [];
                this.displayData();
                this.updateStats();
            } else {
                this.showNotification('لا توجد بيانات محملة', 'warning');
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            this.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }
    
    async loadSavedSettings() {
        try {
            const result = await chrome.storage.local.get(['autoFillSettings']);
            if (result.autoFillSettings) {
                this.selectedFields = new Set(result.autoFillSettings.selectedFields || []);
                this.fieldMappings = result.autoFillSettings.fieldMappings || {};
                this.updateFieldSelections();
                this.updateStats();
            }
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
        }
    }
    
    displayData() {
        if (!this.data.length || !this.columns.length) {
            return;
        }
        
        // إنشاء رأس الجدول
        const headerRow = document.getElementById('table-header');
        headerRow.innerHTML = '';
        
        // عمود التعبئة التلقائية
        const autoFillHeader = document.createElement('th');
        autoFillHeader.className = 'auto-fill-column';
        autoFillHeader.innerHTML = `
            <div class="column-header">
                <span>🎯</span>
                <span>تعبئة تلقائية</span>
            </div>
        `;
        headerRow.appendChild(autoFillHeader);
        
        // أعمدة البيانات
        this.columns.forEach(column => {
            const th = document.createElement('th');
            th.innerHTML = `
                <div class="column-header">
                    <span>📊</span>
                    <span>${column}</span>
                </div>
            `;
            headerRow.appendChild(th);
        });
        
        // إنشاء صفوف البيانات
        const tbody = document.getElementById('table-body');
        tbody.innerHTML = '';
        
        this.data.forEach((row, index) => {
            const tr = document.createElement('tr');
            
            // خانة التعبئة التلقائية
            const autoFillCell = document.createElement('td');
            autoFillCell.className = 'auto-fill-column';
            autoFillCell.innerHTML = `
                <input type="checkbox" 
                       class="auto-fill-checkbox row-checkbox" 
                       data-row-index="${index}"
                       ${this.isRowSelected(index) ? 'checked' : ''}>
            `;
            tr.appendChild(autoFillCell);
            
            // خلايا البيانات
            this.columns.forEach(column => {
                const td = document.createElement('td');
                td.textContent = row[column] || '';
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
    }
    
    setupEventListeners() {
        // تحديد/إلغاء تحديد الكل
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        selectAllCheckbox?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });
        
        // تحديد الصفوف الفردية
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('row-checkbox')) {
                const rowIndex = parseInt(e.target.dataset.rowIndex);
                this.toggleRowSelection(rowIndex, e.target.checked);
            }
        });
        
        // أزرار الإجراءات
        document.getElementById('start-auto-fill')?.addEventListener('click', () => {
            this.startAutoFill();
        });
        
        document.getElementById('save-settings')?.addEventListener('click', () => {
            this.saveSettings();
        });
        
        document.getElementById('toggle-mapping-panel')?.addEventListener('click', () => {
            this.toggleMappingPanel();
        });
        
        // استقبال رسائل من الإضافة
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
        });
    }
    
    toggleSelectAll(checked) {
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        rowCheckboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const rowIndex = parseInt(checkbox.dataset.rowIndex);
            this.toggleRowSelection(rowIndex, checked);
        });
    }
    
    toggleRowSelection(rowIndex, selected) {
        if (selected) {
            this.selectedFields.add(rowIndex);
        } else {
            this.selectedFields.delete(rowIndex);
        }
        
        this.updateStats();
        this.updateSelectAllState();
    }
    
    updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const totalRows = this.data.length;
        const selectedRows = this.selectedFields.size;
        
        if (selectedRows === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedRows === totalRows) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
    
    isRowSelected(rowIndex) {
        return this.selectedFields.has(rowIndex);
    }
    
    updateFieldSelections() {
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        rowCheckboxes.forEach(checkbox => {
            const rowIndex = parseInt(checkbox.dataset.rowIndex);
            checkbox.checked = this.isRowSelected(rowIndex);
        });
        this.updateSelectAllState();
    }
    
    updateStats() {
        document.getElementById('total-rows').textContent = this.data.length;
        document.getElementById('total-columns').textContent = this.columns.length;
        document.getElementById('selected-fields').textContent = this.selectedFields.size;
        
        const percentage = this.data.length > 0 ? 
            Math.round((this.selectedFields.size / this.data.length) * 100) : 0;
        document.getElementById('auto-fill-percentage').textContent = `${percentage}%`;
        
        // تحديث شريط التقدم
        const progressFill = document.getElementById('progress-fill');
        progressFill.style.width = `${percentage}%`;
    }
    
    async saveSettings() {
        try {
            const settings = {
                selectedFields: Array.from(this.selectedFields),
                fieldMappings: this.fieldMappings,
                timestamp: Date.now()
            };
            
            await chrome.storage.local.set({ autoFillSettings: settings });
            
            // إرسال الإعدادات للإضافة الرئيسية
            chrome.runtime.sendMessage({
                action: 'updateAutoFillSettings',
                settings: settings
            });
            
            this.showNotification('تم حفظ الإعدادات بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            this.showNotification('خطأ في حفظ الإعدادات', 'error');
        }
    }
    
    startAutoFill() {
        if (this.selectedFields.size === 0) {
            this.showNotification('يجب تحديد صف واحد على الأقل للتعبئة التلقائية', 'warning');
            return;
        }
        
        // الحصول على أول صف محدد
        const firstSelectedRow = Math.min(...Array.from(this.selectedFields));
        const selectedData = this.data[firstSelectedRow];
        
        if (!selectedData) {
            this.showNotification('خطأ في البيانات المحددة', 'error');
            return;
        }
        
        // إرسال البيانات للتعبئة التلقائية
        chrome.runtime.sendMessage({
            action: 'startSmartAutoFill',
            data: selectedData,
            rowIndex: firstSelectedRow,
            selectedFields: Array.from(this.selectedFields)
        });
        
        this.showNotification(`بدء التعبئة التلقائية للصف ${firstSelectedRow + 1}`, 'info');
        
        // تحديث شريط التقدم
        this.updateProgress(0);
    }
    
    toggleMappingPanel() {
        const panel = document.getElementById('field-mapping-panel');
        panel.classList.toggle('open');
        
        if (panel.classList.contains('open')) {
            this.loadMappingPanel();
        }
    }
    
    loadMappingPanel() {
        const panelContent = document.getElementById('panel-content');
        
        if (!this.columns.length) {
            panelContent.innerHTML = '<p>لا توجد أعمدة للعرض</p>';
            return;
        }
        
        panelContent.innerHTML = this.columns.map(column => `
            <div class="field-item ${this.fieldMappings[column] ? 'mapped' : ''}">
                <h4>${column}</h4>
                <p>نوع الحقل: ${this.getFieldType(column)}</p>
                <p>عدد القيم: ${this.getUniqueValues(column)}</p>
                <button class="btn btn-primary" onclick="dataViewer.mapField('${column}')">
                    ${this.fieldMappings[column] ? 'تعديل الربط' : 'ربط الحقل'}
                </button>
            </div>
        `).join('');
    }
    
    getFieldType(column) {
        if (!this.data.length) return 'غير محدد';
        
        const sampleValue = this.data[0][column];
        if (typeof sampleValue === 'number') return 'رقم';
        if (typeof sampleValue === 'boolean') return 'منطقي';
        if (sampleValue && sampleValue.includes('@')) return 'بريد إلكتروني';
        return 'نص';
    }
    
    getUniqueValues(column) {
        const values = this.data.map(row => row[column]).filter(v => v);
        return new Set(values).size;
    }
    
    mapField(column) {
        // هذه الدالة ستتم إضافتها لاحقاً لربط الحقول
        console.log('ربط الحقل:', column);
    }
    
    updateProgress(percentage) {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `جاري التعبئة... ${percentage}%`;
        
        if (percentage >= 100) {
            progressText.textContent = 'تم الانتهاء من التعبئة التلقائية';
        }
    }
    
    handleMessage(message, sender, sendResponse) {
        console.log('📨 استلام رسالة:', message.action, 'من التبويب:', sender.tab?.id);

        switch (message.action) {
            case 'requestColumnSelection':
                console.log('🎯 طلب اختيار عمود للحقل:', message.fieldInfo);
                this.showSimpleColumnMenu(message.fieldInfo, sender.tab.id);
                break;

            case 'updateProgress':
                this.updateProgress(message.percentage);
                break;

            case 'autoFillComplete':
                this.showNotification('تم الانتهاء من التعبئة التلقائية', 'success');
                this.markRowAsCompleted(message.rowIndex);
                break;

            default:
                console.log('⚠️ رسالة غير معروفة:', message.action);
        }
    }

    showSimpleColumnMenu(fieldInfo, tabId) {
        console.log('📋 إظهار قائمة الأعمدة البسيطة...');

        if (!this.data || this.data.length === 0) {
            console.error('❌ لا توجد بيانات لعرضها');
            this.showNotification('لا توجد بيانات متاحة', 'error');
            return;
        }

        // إزالة القائمة السابقة إن وجدت
        const existing = document.getElementById('simple-column-menu');
        if (existing) existing.remove();

        // إنشاء قائمة بسيطة
        const menu = document.createElement('div');
        menu.id = 'simple-column-menu';
        menu.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10001;
            min-width: 400px;
            max-height: 500px;
            overflow-y: auto;
            direction: rtl;
        `;

        const firstRow = this.data[0] || {};

        menu.innerHTML = `
            <div style="padding: 20px; background: #4CAF50; color: white; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0;">🎯 اختر العمود المناسب</h3>
                <p style="margin: 5px 0 0 0; font-size: 14px;">
                    الحقل: ${fieldInfo.name || fieldInfo.placeholder || 'غير محدد'}
                </p>
            </div>
            <div style="padding: 20px;">
                ${this.columns.map(column => `
                    <div class="simple-column-option" data-column="${column}" style="
                        padding: 15px;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        margin-bottom: 10px;
                        cursor: pointer;
                        transition: all 0.2s;
                        background: white;
                    ">
                        <div style="font-weight: bold; color: #333; margin-bottom: 5px;">
                            📊 ${column}
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            مثال: ${firstRow[column] || 'لا توجد بيانات'}
                        </div>
                    </div>
                `).join('')}
                <div style="margin-top: 20px; text-align: center;">
                    <button id="cancel-column-selection" style="
                        padding: 10px 20px;
                        border: 1px solid #ccc;
                        background: white;
                        border-radius: 6px;
                        cursor: pointer;
                    ">إلغاء</button>
                </div>
            </div>
        `;

        document.body.appendChild(menu);

        // إضافة مستمعي الأحداث
        menu.querySelectorAll('.simple-column-option').forEach(option => {
            option.addEventListener('mouseenter', () => {
                option.style.background = '#f0f8ff';
                option.style.borderColor = '#4CAF50';
            });

            option.addEventListener('mouseleave', () => {
                option.style.background = 'white';
                option.style.borderColor = '#ddd';
            });

            option.addEventListener('click', () => {
                const selectedColumn = option.dataset.column;
                console.log('✅ تم اختيار العمود:', selectedColumn);
                this.handleSimpleColumnSelection(fieldInfo, selectedColumn, tabId);
                menu.remove();
            });
        });

        // زر الإلغاء
        menu.querySelector('#cancel-column-selection').addEventListener('click', () => {
            console.log('❌ تم إلغاء اختيار العمود');
            menu.remove();
        });

        console.log('✅ تم إظهار قائمة الأعمدة البسيطة');
    }

    handleSimpleColumnSelection(fieldInfo, selectedColumn, tabId) {
        console.log('🔗 معالجة اختيار العمود:', selectedColumn, 'للحقل:', fieldInfo.name);

        // الحصول على القيمة من الصف الأول
        const firstRowValue = this.data[0] ? this.data[0][selectedColumn] : '';

        // إرسال المطابقة للتبويب
        chrome.tabs.sendMessage(tabId, {
            action: 'applySimpleMapping',
            fieldSelector: fieldInfo.selector,
            column: selectedColumn,
            value: firstRowValue,
            fieldInfo: fieldInfo
        }).then(() => {
            console.log('✅ تم إرسال المطابقة للتبويب');
            this.showNotification(`تم ربط الحقل بالعمود: ${selectedColumn}`, 'success');
        }).catch(error => {
            console.error('❌ خطأ في إرسال المطابقة:', error);
        });
    }

    markRowAsCompleted(rowIndex) {
        // وضع علامة على الصف المكتمل في الجدول
        const tableRows = document.querySelectorAll('#table-body tr');
        if (tableRows[rowIndex]) {
            const row = tableRows[rowIndex];

            // إضافة علامة الإكمال
            row.style.background = 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)';
            row.style.border = '2px solid #28a745';

            // إضافة أيقونة الإكمال
            const firstCell = row.querySelector('td');
            if (firstCell && !firstCell.querySelector('.completion-icon')) {
                const icon = document.createElement('span');
                icon.className = 'completion-icon';
                icon.innerHTML = '✅';
                icon.style.cssText = `
                    position: absolute;
                    right: -10px;
                    top: 50%;
                    transform: translateY(-50%);
                    font-size: 16px;
                    z-index: 10;
                `;
                firstCell.style.position = 'relative';
                firstCell.appendChild(icon);
            }

            // تحديث خانة الاختيار
            const checkbox = row.querySelector('.row-checkbox');
            if (checkbox) {
                checkbox.checked = false;
                checkbox.disabled = true;
                checkbox.style.opacity = '0.5';
            }
        }

        // تحديث الإحصائيات
        this.updateCompletionStats();
    }

    updateCompletionStats() {
        const completedRows = document.querySelectorAll('#table-body tr[style*="28a745"]').length;
        const totalRows = this.data.length;
        const completionPercentage = totalRows > 0 ? Math.round((completedRows / totalRows) * 100) : 0;

        // تحديث شريط التقدم
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        if (progressFill) {
            progressFill.style.width = `${completionPercentage}%`;
        }

        if (progressText) {
            progressText.textContent = `تم إكمال ${completedRows} من ${totalRows} صف (${completionPercentage}%)`;
        }

        // إضافة إحصائية جديدة
        const completedElement = document.getElementById('completed-rows');
        if (completedElement) {
            completedElement.textContent = completedRows;
        }
    }

    async sendNextRowData(tabId) {
        try {
            // العثور على أول صف غير مكتمل
            const tableRows = document.querySelectorAll('#table-body tr');
            let nextRowIndex = -1;

            for (let i = 0; i < tableRows.length; i++) {
                const row = tableRows[i];
                if (!row.style.border?.includes('28a745')) { // غير مكتمل
                    const checkbox = row.querySelector('.row-checkbox');
                    if (checkbox && checkbox.checked) {
                        nextRowIndex = i;
                        break;
                    }
                }
            }

            if (nextRowIndex >= 0 && this.data[nextRowIndex]) {
                // إرسال بيانات الصف التالي
                chrome.tabs.sendMessage(tabId, {
                    action: 'fillNextRow',
                    rowData: this.data[nextRowIndex],
                    rowIndex: nextRowIndex
                });

                this.showNotification(`جاري تعبئة الصف ${nextRowIndex + 1}`, 'info');
            } else {
                // لا توجد صفوف أخرى للتعبئة
                chrome.tabs.sendMessage(tabId, {
                    action: 'allRowsCompleted'
                });

                this.showNotification('تم إكمال جميع الصفوف المحددة', 'success');
            }

        } catch (error) {
            console.error('خطأ في إرسال الصف التالي:', error);
        }
    }

    async sendCurrentRowData(tabId) {
        try {
            const currentRowData = this.getCurrentRowData();

            if (currentRowData) {
                chrome.tabs.sendMessage(tabId, {
                    action: 'receiveCurrentRowData',
                    rowData: currentRowData,
                    rowIndex: this.currentRowIndex,
                    columns: this.columns
                });

                // تحديث مؤشر وضع التعلم
                this.updateLearningIndicator();

            } else {
                chrome.tabs.sendMessage(tabId, {
                    action: 'noDataAvailable'
                });
            }

        } catch (error) {
            console.error('خطأ في إرسال بيانات الصف الحالي:', error);
        }
    }

    handleFieldMappingCompleted(mapping, tabId) {
        // تحديث إحصائيات الربط
        this.updateMappingStats();

        // تمييز العمود المربوط
        this.highlightMappedColumn(mapping.column);

        // إرسال بيانات الصف الحالي للحقول الأخرى
        this.sendCurrentRowData(tabId);

        this.showNotification(`تم ربط الحقل: ${mapping.column}`, 'success');
    }

    async moveToNextRow(tabId) {
        try {
            // تمييز الصف الحالي كمكتمل
            this.markRowAsCompleted(this.currentRowIndex);
            this.completedRows.add(this.currentRowIndex);

            // البحث عن الصف التالي
            const nextRowIndex = this.findNextAvailableRow();

            if (nextRowIndex !== -1) {
                this.currentRowIndex = nextRowIndex;

                // إرسال بيانات الصف الجديد
                chrome.tabs.sendMessage(tabId, {
                    action: 'receiveCurrentRowData',
                    rowData: this.data[this.currentRowIndex],
                    rowIndex: this.currentRowIndex,
                    columns: this.columns,
                    isNewRow: true
                });

                // تحديث مؤشر وضع التعلم
                this.updateLearningIndicator();

                this.showNotification(`انتقال للصف ${this.currentRowIndex + 1}`, 'info');

            } else {
                // لا توجد صفوف أخرى
                chrome.tabs.sendMessage(tabId, {
                    action: 'allRowsCompleted'
                });

                this.showNotification('🎉 تم إكمال جميع الصفوف!', 'success');
                this.disableLearningMode();
            }

        } catch (error) {
            console.error('خطأ في الانتقال للصف التالي:', error);
        }
    }

    findNextAvailableRow() {
        const tableRows = document.querySelectorAll('#table-body tr');

        for (let i = 0; i < tableRows.length; i++) {
            const row = tableRows[i];
            const checkbox = row.querySelector('.row-checkbox');

            // البحث عن صف محدد وغير مكتمل
            if (checkbox && checkbox.checked && !this.completedRows.has(i)) {
                return i;
            }
        }

        return -1; // لا توجد صفوف متاحة
    }

    updateLearningIndicator() {
        const indicator = document.getElementById('learning-mode-indicator');
        if (indicator) {
            const rowInfo = indicator.querySelector('span:last-child');
            if (rowInfo) {
                rowInfo.textContent = `الصف: ${this.currentRowIndex + 1}`;
            }

            // تأثير بصري للتحديث
            indicator.style.transform = 'scale(1.1)';
            setTimeout(() => {
                indicator.style.transform = 'scale(1)';
            }, 200);
        }
    }

    disableLearningMode() {
        this.isLearningMode = false;

        // إزالة مؤشر وضع التعلم
        const indicator = document.getElementById('learning-mode-indicator');
        if (indicator) {
            indicator.style.animation = 'fadeOut 0.5s ease-out';
            setTimeout(() => {
                indicator.remove();
            }, 500);
        }

        // إضافة أنيميشن الاختفاء
        if (!document.getElementById('fadeout-animation')) {
            const style = document.createElement('style');
            style.id = 'fadeout-animation';
            style.textContent = `
                @keyframes fadeOut {
                    from { opacity: 1; transform: scale(1); }
                    to { opacity: 0; transform: scale(0.8); }
                }
            `;
            document.head.appendChild(style);
        }

        // إرسال إشعار إيقاف وضع التعلم لجميع التبويبات
        this.activeConnections.forEach((connection, tabId) => {
            chrome.tabs.sendMessage(tabId, {
                action: 'disableLearningMode'
            }).catch(() => {
                // تجاهل الأخطاء
            });
        });
    }
    
    setupGlobalRightClickSystem() {
        // إرسال رسالة لجميع التبويبات لتفعيل نظام النقر الأيمن
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'setupRightClickSystem',
                        dataViewerTabId: chrome.tabs.getCurrent().then(currentTab => currentTab.id)
                    }).catch(() => {
                        // تجاهل الأخطاء
                    });
                }
            });
        });
    }

    showColumnSelectionMenu(fieldInfo, tabId) {
        // إنشاء قائمة اختيار الأعمدة
        const menu = document.createElement('div');
        menu.className = 'column-selection-menu';
        menu.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #4facfe;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            min-width: 400px;
            max-height: 500px;
            overflow-y: auto;
            direction: rtl;
        `;

        menu.innerHTML = `
            <div style="padding: 20px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border-radius: 10px 10px 0 0;">
                <h3 style="margin: 0; font-size: 18px;">🎯 اختر العمود المناسب</h3>
                <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;">
                    الحقل: ${fieldInfo.label || fieldInfo.name || fieldInfo.placeholder || 'غير محدد'}
                </p>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 15px; font-size: 14px; color: #666;">
                    اختر العمود الذي يطابق هذا الحقل:
                </div>
                <div class="columns-list">
                    ${this.columns.map((column, index) => `
                        <div class="column-option" data-column="${column}" style="
                            padding: 12px;
                            border: 1px solid #dee2e6;
                            border-radius: 6px;
                            margin-bottom: 8px;
                            cursor: pointer;
                            transition: all 0.2s;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        ">
                            <div>
                                <strong>${column}</strong>
                                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                                    مثال: ${this.getFirstRowValue(column)}
                                </div>
                            </div>
                            <div style="background: #e3f2fd; padding: 4px 8px; border-radius: 4px; font-size: 11px;">
                                ${this.getColumnType(column)}
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                    <button class="btn-cancel" style="
                        padding: 8px 16px;
                        border: 1px solid #ccc;
                        background: white;
                        border-radius: 6px;
                        cursor: pointer;
                    ">إلغاء</button>
                </div>
            </div>
        `;

        document.body.appendChild(menu);

        // إضافة مستمعي الأحداث
        menu.querySelectorAll('.column-option').forEach(option => {
            option.addEventListener('mouseenter', () => {
                option.style.background = '#f0f8ff';
                option.style.borderColor = '#4facfe';
            });

            option.addEventListener('mouseleave', () => {
                option.style.background = 'white';
                option.style.borderColor = '#dee2e6';
            });

            option.addEventListener('click', () => {
                const selectedColumn = option.dataset.column;
                this.handleColumnSelection(fieldInfo, selectedColumn, tabId);
                menu.remove();
            });
        });

        menu.querySelector('.btn-cancel').addEventListener('click', () => {
            menu.remove();
        });

        // إغلاق عند النقر خارج القائمة
        setTimeout(() => {
            document.addEventListener('click', (e) => {
                if (!menu.contains(e.target)) {
                    menu.remove();
                }
            }, { once: true });
        }, 100);
    }

    getFirstRowValue(column) {
        if (this.data.length > 0 && this.data[0][column]) {
            const value = this.data[0][column].toString();
            return value.length > 20 ? value.substring(0, 20) + '...' : value;
        }
        return 'لا توجد بيانات';
    }

    getColumnType(column) {
        if (!this.data.length) return 'غير محدد';

        const sampleValue = this.data[0][column];
        if (typeof sampleValue === 'number') return 'رقم';
        if (typeof sampleValue === 'boolean') return 'منطقي';
        if (sampleValue && sampleValue.toString().includes('@')) return 'إيميل';
        return 'نص';
    }

    async handleColumnSelection(fieldInfo, selectedColumn, tabId) {
        try {
            // حفظ المطابقة
            const mapping = {
                fieldSelector: fieldInfo.selector,
                column: selectedColumn,
                fieldInfo: fieldInfo,
                timestamp: Date.now(),
                confidence: 1.0 // ثقة عالية للاختيار اليدوي
            };

            // حفظ في التخزين المحلي
            const result = await chrome.storage.local.get(['fieldMappings']);
            const fieldMappings = result.fieldMappings || {};
            fieldMappings[fieldInfo.selector] = mapping;
            await chrome.storage.local.set({ fieldMappings });

            // جلب القيمة من الصف الحالي
            const currentRowData = this.getCurrentRowData();
            const currentValue = currentRowData ? currentRowData[selectedColumn] : this.getFirstRowValue(selectedColumn);

            // إرسال المطابقة والقيمة للتبويب المصدر
            chrome.tabs.sendMessage(tabId, {
                action: 'applyFieldMappingWithData',
                mapping: mapping,
                value: currentValue,
                rowData: currentRowData,
                rowIndex: this.currentRowIndex
            });

            // تحديث الإحصائيات
            this.updateMappingStats();

            // تمييز العمود المربوط في الجدول
            this.highlightMappedColumn(selectedColumn);

            this.showNotification(`✅ تم ربط الحقل بالعمود: ${selectedColumn}`, 'success');

        } catch (error) {
            console.error('خطأ في حفظ المطابقة:', error);
            this.showNotification('خطأ في حفظ المطابقة', 'error');
        }
    }

    getCurrentRowData() {
        // العثور على أول صف غير مكتمل ومحدد
        const tableRows = document.querySelectorAll('#table-body tr');

        for (let i = 0; i < tableRows.length; i++) {
            const row = tableRows[i];
            const checkbox = row.querySelector('.row-checkbox');

            // التحقق من أن الصف محدد وغير مكتمل
            if (checkbox && checkbox.checked && !this.completedRows.has(i)) {
                this.currentRowIndex = i;
                return this.data[i];
            }
        }

        // إذا لم يتم العثور على صف، استخدم الصف الأول
        this.currentRowIndex = 0;
        return this.data[0];
    }

    highlightMappedColumn(columnName) {
        // العثور على فهرس العمود
        const columnIndex = this.columns.indexOf(columnName);
        if (columnIndex === -1) return;

        // تمييز رأس العمود
        const headerCells = document.querySelectorAll('#table-header th');
        const targetHeader = headerCells[columnIndex + 1]; // +1 لأن العمود الأول هو التعبئة التلقائية

        if (targetHeader) {
            targetHeader.style.background = 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)';
            targetHeader.style.border = '2px solid #28a745';

            // إضافة أيقونة الربط
            const linkIcon = document.createElement('span');
            linkIcon.innerHTML = ' 🔗';
            linkIcon.style.color = '#28a745';
            targetHeader.appendChild(linkIcon);
        }

        // تمييز خلايا العمود
        const bodyRows = document.querySelectorAll('#table-body tr');
        bodyRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const targetCell = cells[columnIndex + 1]; // +1 لأن العمود الأول هو التعبئة التلقائية

            if (targetCell) {
                targetCell.style.background = 'rgba(212, 237, 218, 0.3)';
                targetCell.style.border = '1px solid #c3e6cb';
            }
        });
    }

    updateMappingStats() {
        // تحديث إحصائيات الربط في الواجهة
        chrome.storage.local.get(['fieldMappings']).then(result => {
            const mappings = result.fieldMappings || {};
            const mappedCount = Object.keys(mappings).length;

            // تحديث العداد في الواجهة
            const mappedFieldsElement = document.getElementById('mapped-fields-count');
            if (mappedFieldsElement) {
                mappedFieldsElement.textContent = mappedCount;
            }
        });
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// تهيئة عارض البيانات
const dataViewer = new SmartDataViewer();

// جعل المتغير متاح عالمياً للاستخدام في HTML
window.dataViewer = dataViewer;
