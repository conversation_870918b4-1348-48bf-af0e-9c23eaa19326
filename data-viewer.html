<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عارض البيانات - Smart Form Filler</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.8em;
            font-weight: 300;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .content {
            padding: 30px;
        }
        
        .stats-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        
        .stat-icon {
            font-size: 18px;
        }
        
        .data-table-container {
            overflow-x: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            white-space: nowrap;
        }
        
        .data-table th {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .auto-fill-column {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-left: 3px solid #4caf50;
        }
        
        .auto-fill-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }
        
        .auto-fill-checkbox:checked {
            accent-color: #4caf50;
        }
        
        .column-header {
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }
        
        .select-all-container {
            margin-bottom: 15px;
            padding: 10px 15px;
            background: #e3f2fd;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.3s ease;
            width: 0%;
        }
        
        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 500;
            color: #666;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideDown 0.3s ease-out;
        }
        
        .notification.success {
            background: #28a745;
        }
        
        .notification.info {
            background: #17a2b8;
        }
        
        .notification.warning {
            background: #ffc107;
            color: #212529;
        }
        
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); }
            to { transform: translateX(-50%) translateY(0); }
        }
        
        .field-mapping-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }
        
        .field-mapping-panel.open {
            right: 0;
        }
        
        .panel-header {
            padding: 20px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .panel-content {
            padding: 20px;
        }
        
        .field-item {
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }
        
        .field-item.mapped {
            background: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 عارض البيانات المحملة</h1>
            <div class="header-actions">
                <button class="btn btn-warning" id="toggle-mapping-panel">
                    🎯 إدارة الحقول
                </button>
                <button class="btn btn-success" id="start-auto-fill">
                    ⚡ بدء التعبئة التلقائية
                </button>
                <button class="btn btn-primary" id="save-settings">
                    💾 حفظ الإعدادات
                </button>
            </div>
        </div>
        
        <div class="content">
            <div class="stats-bar">
                <div class="stat-item">
                    <span class="stat-icon">📊</span>
                    <span>إجمالي الصفوف: <strong id="total-rows">0</strong></span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">📋</span>
                    <span>إجمالي الأعمدة: <strong id="total-columns">0</strong></span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">✅</span>
                    <span>الحقول المحددة: <strong id="selected-fields">0</strong></span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">🎯</span>
                    <span>نسبة التعبئة التلقائية: <strong id="auto-fill-percentage">0%</strong></span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">✅</span>
                    <span>الصفوف المكتملة: <strong id="completed-rows">0</strong></span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">🔗</span>
                    <span>الحقول المربوطة: <strong id="mapped-fields-count">0</strong></span>
                </div>
            </div>
            
            <div class="select-all-container">
                <input type="checkbox" id="select-all-checkbox" class="auto-fill-checkbox">
                <label for="select-all-checkbox">
                    <strong>تحديد/إلغاء تحديد جميع الحقول للتعبئة التلقائية</strong>
                </label>
                <span style="margin-right: auto; color: #666; font-size: 12px;">
                    الحقول المحددة ستتم تعبئتها تلقائياً عند إنشاء نماذج جديدة
                </span>
            </div>
            
            <div class="data-table-container">
                <table class="data-table" id="data-table">
                    <thead id="table-header">
                        <!-- سيتم إنشاء الرؤوس ديناميكياً -->
                    </thead>
                    <tbody id="table-body">
                        <!-- سيتم إنشاء البيانات ديناميكياً -->
                    </tbody>
                </table>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text" id="progress-text">
                جاهز للتعبئة التلقائية
            </div>
        </div>
    </div>
    
    <!-- لوحة إدارة الحقول -->
    <div class="field-mapping-panel" id="field-mapping-panel">
        <div class="panel-header">
            <h3>🎯 إدارة ربط الحقول</h3>
            <p>ربط أعمدة البيانات بحقول النماذج</p>
        </div>
        <div class="panel-content" id="panel-content">
            <!-- سيتم إنشاء المحتوى ديناميكياً -->
        </div>
    </div>
    
    <script src="data-viewer.js"></script>
</body>
</html>
