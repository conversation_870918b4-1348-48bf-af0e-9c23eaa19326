# تعليمات التثبيت السريع - Smart Form Filler

## 🚀 التثبيت في Chrome

### الخطوة 1: تحضير الملفات
1. تأكد من وجود جميع الملفات في المجلد
2. تحقق من وجود ملف `manifest.json`

### الخطوة 2: تثبيت الإضافة
1. افتح متصفح Chrome
2. اذهب إلى `chrome://extensions/`
3. فعل "وضع المطور" (Developer mode) في الزاوية العلوية اليمنى
4. انقر على "تحميل إضافة غير مُعبأة" (Load unpacked)
5. اختر مجلد المشروع الذي يحتوي على `manifest.json`
6. انقر "اختيار مجلد"

### الخطوة 3: التحقق من التثبيت
- ستظهر أيقونة الإضافة في شريط الأدوات
- يجب أن ترى "Smart Form Filler" في قائمة الإضافات

## 🦊 التثبيت في Firefox

### الخطوة 1: تحضير الملفات
1. تأكد من وجود جميع الملفات
2. تحقق من صحة ملف `manifest.json`

### الخطوة 2: تثبيت الإضافة
1. افتح متصفح Firefox
2. اذهب إلى `about:debugging`
3. انقر على "This Firefox" من الجانب الأيسر
4. انقر على "Load Temporary Add-on"
5. اختر ملف `manifest.json` من مجلد المشروع

### الخطوة 3: التحقق من التثبيت
- ستظهر الإضافة في قائمة "Temporary Extensions"
- يجب أن ترى أيقونة الإضافة في شريط الأدوات

## 🔧 الاختبار السريع

### 1. اختبار الواجهة
1. انقر على أيقونة الإضافة
2. يجب أن تظهر واجهة المستخدم
3. تحقق من ظهور جميع الأقسام

### 2. اختبار رفع الملفات
1. انقر على "اختر ملف"
2. ارفع ملف `sample-data.csv` المرفق
3. يجب أن تظهر معاينة البيانات

### 3. اختبار وضع التعليم
1. افتح ملف `test-form.html` في المتصفح
2. فعل وضع التعليم من الإضافة
3. انقر بالزر الأيمن على حقول النموذج
4. يجب أن تظهر قائمة الأعمدة

### 4. اختبار التعبئة التلقائية
1. اربط بعض الحقول بالأعمدة
2. احفظ النمط
3. اختر صف من البيانات
4. انقر "بدء التعبئة التلقائية"

## ❗ حل المشاكل الشائعة

### المشكلة: الإضافة لا تظهر
**الحل:**
- تأكد من تفعيل وضع المطور
- تحقق من وجود جميع الملفات المطلوبة
- أعد تحميل الإضافة

### المشكلة: خطأ في manifest.json
**الحل:**
- تحقق من صحة تنسيق JSON
- تأكد من وجود جميع الحقول المطلوبة
- راجع console للأخطاء

### المشكلة: لا تعمل في صفحة معينة
**الحل:**
- تحقق من أن الصفحة تحتوي على نماذج
- افتح Developer Tools وراجع Console
- تأكد من تحميل content script

### المشكلة: البيانات لا تُحفظ
**الحل:**
- تحقق من صلاحيات التخزين في manifest.json
- امسح بيانات الإضافة وأعد المحاولة
- تحقق من مساحة التخزين المتاحة

## 📁 الملفات المطلوبة

تأكد من وجود هذه الملفات:
```
✅ manifest.json
✅ popup.html
✅ popup.js
✅ content.js
✅ background.js
✅ utils.js
✅ styles.css
✅ content-styles.css
✅ icons/ (مجلد الأيقونات)
```

## 🧪 ملفات الاختبار

الملفات التجريبية المرفقة:
```
📄 sample-data.csv - بيانات تجريبية CSV
📄 sample-data.json - بيانات تجريبية JSON
🌐 test-form.html - نموذج تجريبي للاختبار
```

## 🔄 إعادة التحميل

عند تعديل الكود:
1. اذهب إلى `chrome://extensions/`
2. انقر على أيقونة إعادة التحميل بجانب الإضافة
3. أو استخدم Ctrl+R في صفحة الإضافات

## 📞 الدعم

إذا واجهت مشاكل:
1. راجع ملف `README_DEVELOPER.md` للتفاصيل التقنية
2. تحقق من console للأخطاء
3. تأكد من تحديث المتصفح لآخر إصدار

## ✅ قائمة التحقق النهائية

قبل الاستخدام، تأكد من:
- [ ] تثبيت الإضافة بنجاح
- [ ] ظهور الأيقونة في شريط الأدوات
- [ ] عمل واجهة المستخدم
- [ ] إمكانية رفع الملفات
- [ ] عمل وضع التعليم
- [ ] نجاح التعبئة التلقائية

🎉 **مبروك! الإضافة جاهزة للاستخدام**
