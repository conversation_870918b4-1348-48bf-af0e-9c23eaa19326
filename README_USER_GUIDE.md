# Smart Form Filler - دليل المستخدم الشامل

## الفكرة الأساسية
إضافة للمتصفح تسمح للمستخدمين بتعبئة النماذج عبر الإنترنت بشكل تلقائي باستخدام بيانات مخزنة مسبقاً، مع إمكانية تخصيص عملية التعبئة بشكل كامل.

## خريطة الإجراءات للمستخدم - دليل شامل

### المرحلة الأولى: إعداد البيانات والتحضير

#### 1. رفع ملف البيانات
- قم برفع ملف البيانات (CSV أو JSON) الذي يحتوي على المعلومات المراد استخدامها
- تأكد من تنظيم البيانات في جداول مع تسمية الأعمدة بأسماء واضحة ومفهومة
- مثال: عمود "الاسم"، عمود "البريد الإلكتروني"، عمود "رقم الهاتف"

#### 2. فتح تبويب جديد
- **مهم جداً**: بعد رفع الملف، افتح تبويب جديد في نفس المتصفح
- انتقل إلى الصفحة التي تحتوي على النموذج المراد تعبئته
- تأكد من أن الصفحة محملة بالكامل قبل البدء

### المرحلة الثانية: إعداد النمط (Pattern Setup)

#### 3. تفعيل وضع التعليم
- انقر على أيقونة الإضافة في شريط الأدوات
- اختر "وضع التعليم" أو "إنشاء نمط جديد"
- ستظهر واجهة الإضافة مع خيارات التخصيص

#### 4. تحديد زر الإنشاء ومدة الانتظار
- **خطوة مهمة**: حدد زر "إنشاء" أو "إرسال" في النموذج
- اضبط مدة الانتظار (بالثواني) لظهور الشاشة المنبثقة أو الصفحة التالية
- القيمة المقترحة: 2-5 ثواني حسب سرعة الموقع
- هذا يضمن أن الإضافة تنتظر تحميل العناصر بالكامل

### المرحلة الثالثة: ربط الحقول بالبيانات

#### 5. ربط الحقول بأعمدة البيانات
- انقر بالزر الأيمن على أي حقل في النموذج
- ستظهر قائمة تحتوي على أسماء جميع الأعمدة من الجدول المرفوع
- اختر العمود المناسب لهذا الحقل
- مثال: حقل "الاسم الأول" ← اختر عمود "الاسم"

#### 6. التعامل مع الحقول الإضافية
- إذا كانت الأعمدة في الجدول أقل من الحقول في النموذج:
  - يمكنك اختيار "قيمة ثابتة" للحقول الإضافية
  - هذه القيمة ستتكرر تلقائياً في جميع عمليات التعبئة
  - مثال: حقل "البلد" ← قيمة ثابتة "السعودية"

#### 7. التعامل مع القوائم المنسدلة
- عند النقر بالزر الأيمن على قائمة منسدلة:
  - ستظهر خيارات القائمة المنسدلة
  - حدد اسم القيمة المطلوبة من البيانات
  - يمكنك ربطها بعمود من الجدول أو تحديد قيمة ثابتة
  - مثال: قائمة "الجنس" ← ربط بعمود "النوع" أو قيمة ثابتة "ذكر"

#### 8. حفظ النمط
- بعد ربط جميع الحقول، انقر على زر "حفظ النمط"
- أعط النمط اسماً وصفياً (مثال: "نموذج التسجيل - موقع X")
- يمكنك إضافة وصف مختصر للنمط

### المرحلة الرابعة: استخدام النمط

#### 9. التعبئة التلقائية
- عند زيارة نفس الصفحة مرة أخرى، ستظهر أيقونة تنبيه من الإضافة
- انقر على الأيقونة لفتح قائمة الأنماط المحفوظة
- اختر النمط المناسب للصفحة الحالية

#### 10. اختيار البيانات
- اختر الصف من البيانات المراد استخدامه
- يمكنك معاينة البيانات قبل التعبئة
- انقر على "تعبئة تلقائية" لبدء العملية

#### 11. المراجعة والإرسال
- راجع البيانات المعبأة للتأكد من صحتها
- قم بأي تعديلات ضرورية يدوياً
- انقر على زر الإرسال/الحفظ في النموذج

## مخطط تدفق العمليات

```
1. رفع ملف البيانات
   ↓
2. فتح تبويب جديد → الانتقال للصفحة المطلوبة
   ↓
3. تفعيل وضع التعليم
   ↓
4. تحديد زر الإنشاء + ضبط مدة الانتظار
   ↓
5. النقر بالزر الأيمن على الحقول → ربطها بالأعمدة
   ↓
6. التعامل مع القوائم المنسدلة والحقول الإضافية
   ↓
7. حفظ النمط
   ↓
8. استخدام النمط للتعبئة التلقائية
```

## نصائح مهمة للاستخدام الأمثل

### نصائح عامة:
- احرص على تسمية أعمدة البيانات بأسماء واضحة ومفهومة
- اختبر النمط على بيانات تجريبية قبل الاستخدام الفعلي
- احفظ نسخة احتياطية من ملفات البيانات
- راجع البيانات المعبأة دائماً قبل الإرسال

### استكشاف الأخطاء:
- إذا لم تظهر القائمة عند النقر بالزر الأيمن، تأكد من تفعيل وضع التعليم
- إذا لم تعمل التعبئة التلقائية، تحقق من مدة الانتظار المحددة
- إذا ظهرت بيانات خاطئة، راجع ربط الحقول بالأعمدة

### ميزات متقدمة:
- يمكن حفظ أنماط متعددة لمواقع مختلفة
- إمكانية التشغيل التلقائي عند زيارة مواقع محددة
- دعم التعبئة المتسلسلة لعدة صفحات متتالية
- مشاركة الأنماط مع مستخدمين آخرين

## تفاصيل تقنية مهمة

### متطلبات النظام:
- متصفح Chrome أو Firefox أو Edge
- إصدار حديث من المتصفح (آخر 3 إصدارات)
- اتصال بالإنترنت لتحميل البيانات

### أنواع الملفات المدعومة:
- **CSV**: ملفات القيم المفصولة بفواصل
- **JSON**: ملفات البيانات المنظمة
- **Excel**: ملفات .xlsx (سيتم تحويلها تلقائياً)

### أنواع الحقول المدعومة:
- حقول النص العادي (input type="text")
- حقول البريد الإلكتروني (input type="email")
- حقول كلمات المرور (input type="password")
- حقول الأرقام (input type="number")
- القوائم المنسدلة (select)
- مربعات الاختيار (checkbox)
- أزرار الاختيار (radio buttons)
- مناطق النص الكبيرة (textarea)

### خطوات استكشاف الأخطاء المتقدمة:

#### مشكلة: لا تظهر قائمة الأعمدة عند النقر بالزر الأيمن
**الحلول:**
1. تأكد من تفعيل وضع التعليم
2. تحقق من أن ملف البيانات تم رفعه بنجاح
3. أعد تحميل الصفحة وحاول مرة أخرى
4. تأكد من أن الحقل قابل للتحرير

#### مشكلة: التعبئة التلقائية لا تعمل
**الحلول:**
1. تحقق من مدة الانتظار المحددة (زدها إذا لزم الأمر)
2. تأكد من حفظ النمط بشكل صحيح
3. تحقق من أن جميع الحقول مربوطة بشكل صحيح
4. تأكد من أن البيانات متوفرة في الملف المرفوع

#### مشكلة: بيانات خاطئة في الحقول
**الحلول:**
1. راجع ربط الحقول بالأعمدة
2. تحقق من تنسيق البيانات في الملف الأصلي
3. تأكد من عدم وجود أعمدة فارغة أو قيم null
4. راجع إعدادات القيم الثابتة

### أمثلة عملية:

#### مثال 1: نموذج التسجيل في موقع تعليمي
```
البيانات المطلوبة:
- الاسم الأول
- الاسم الأخير
- البريد الإلكتروني
- رقم الهاتف
- البلد
- المدينة

ملف CSV:
الاسم_الأول,الاسم_الأخير,البريد,الهاتف,البلد,المدينة
أحمد,محمد,<EMAIL>,0501234567,السعودية,الرياض
فاطمة,علي,<EMAIL>,0507654321,السعودية,جدة
```

#### مثال 2: نموذج طلب وظيفة
```
البيانات المطلوبة:
- الاسم الكامل
- المؤهل العلمي
- سنوات الخبرة
- المهارات
- الراتب المطلوب

ملف JSON:
[
  {
    "name": "سارة أحمد",
    "education": "بكالوريوس هندسة",
    "experience": "5",
    "skills": "برمجة، تصميم",
    "salary": "8000"
  }
]
```

## الفئة المستهدفة
هذه الإضافة مثالية للأشخاص الذين يتعاملون مع نماذج متكررة، مثل:
- مديري المحتوى
- مسؤولي التسويق
- محترفي إدخال البيانات
- أي شخص يحتاج لإدخال بيانات متشابهة بشكل متكرر

## الدعم والمساعدة
- للمساعدة التقنية: راجع قسم استكشاف الأخطاء أعلاه
- للاقتراحات والتحسينات: استخدم نظام التغذية الراجعة في الإضافة
- للتدريب: راجع الأمثلة العملية المرفقة
