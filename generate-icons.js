// Script to generate icon files for Smart Form Filler
const fs = require('fs');
const { createCanvas } = require('canvas');

function createIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Background gradient (simplified to solid color for Node.js)
    ctx.fillStyle = '#4facfe';
    ctx.fillRect(0, 0, size, size);
    
    // Rounded corners effect
    ctx.globalCompositeOperation = 'destination-in';
    ctx.beginPath();
    ctx.roundRect(0, 0, size, size, size * 0.2);
    ctx.fill();
    ctx.globalCompositeOperation = 'source-over';
    
    // Form background
    const formSize = size * 0.6;
    const formX = (size - formSize) / 2;
    const formY = (size - formSize) / 2;
    
    ctx.fillStyle = 'white';
    ctx.fillRect(formX, formY, formSize, formSize);
    
    // Form lines
    ctx.fillStyle = '#4facfe';
    const lineHeight = size * 0.03;
    const lineSpacing = size * 0.08;
    const lineStartX = formX + size * 0.1;
    const lineWidth = formSize * 0.7;
    
    for (let i = 0; i < 4; i++) {
        const y = formY + size * 0.15 + (i * lineSpacing);
        ctx.fillRect(lineStartX, y, lineWidth * (0.7 + Math.random() * 0.3), lineHeight);
    }
    
    return canvas.toBuffer('image/png');
}

// Generate icons
const sizes = [16, 32, 48, 128];

sizes.forEach(size => {
    try {
        const iconBuffer = createIcon(size);
        fs.writeFileSync(`icons/icon${size}.png`, iconBuffer);
        console.log(`Created icon${size}.png`);
    } catch (error) {
        console.log(`Could not create icon${size}.png - using alternative method`);
    }
});

console.log('Icon generation complete!');
