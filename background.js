// Smart Form Filler - Background Script

class SmartFormBackground {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupContextMenus();
    }
    
    setupEventListeners() {
        // Extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                this.onInstall();
            } else if (details.reason === 'update') {
                this.onUpdate(details.previousVersion);
            }
        });
        
        // Tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.onTabComplete(tabId, tab);
            }
        });
        
        // Message handling
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
        
        // Storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.onStorageChanged(changes, namespace);
        });
    }
    
    setupContextMenus() {
        // Remove existing context menus
        chrome.contextMenus.removeAll(() => {
            // Create main context menu
            chrome.contextMenus.create({
                id: 'smart-form-main',
                title: 'Smart Form Filler',
                contexts: ['page']
            });
            
            chrome.contextMenus.create({
                id: 'smart-form-teaching-mode',
                parentId: 'smart-form-main',
                title: 'تفعيل وضع التعليم',
                contexts: ['page']
            });
            
            chrome.contextMenus.create({
                id: 'smart-form-auto-fill',
                parentId: 'smart-form-main',
                title: 'التعبئة التلقائية',
                contexts: ['page']
            });
            
            chrome.contextMenus.create({
                id: 'smart-form-separator',
                parentId: 'smart-form-main',
                type: 'separator',
                contexts: ['page']
            });
            
            chrome.contextMenus.create({
                id: 'smart-form-settings',
                parentId: 'smart-form-main',
                title: 'الإعدادات',
                contexts: ['page']
            });
        });
        
        // Context menu click handler
        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenuClick(info, tab);
        });
    }
    
    onInstall() {
        console.log('Smart Form Filler installed');
        
        // Initialize default settings
        const defaultSettings = {
            autoDetectForms: true,
            showNotifications: true,
            defaultWaitTime: 3000,
            autoSubmit: false,
            theme: 'light'
        };
        
        chrome.storage.local.set({ settings: defaultSettings });
        
        // Show welcome notification
        this.showNotification('مرحباً بك في Smart Form Filler!', 'تم تثبيت الإضافة بنجاح');
    }
    
    onUpdate(previousVersion) {
        console.log(`Smart Form Filler updated from ${previousVersion}`);
        
        // Handle version-specific updates
        this.migrateData(previousVersion);
    }
    
    async onTabComplete(tabId, tab) {
        try {
            // Check if this page has saved patterns
            const result = await chrome.storage.local.get(['savedPatterns']);
            const savedPatterns = result.savedPatterns || [];
            
            const currentUrl = this.getCleanUrl(tab.url);
            const matchingPatterns = savedPatterns.filter(pattern => pattern.url === currentUrl);
            
            if (matchingPatterns.length > 0) {
                // Update badge to indicate available patterns
                chrome.action.setBadgeText({
                    tabId: tabId,
                    text: matchingPatterns.length.toString()
                });
                
                chrome.action.setBadgeBackgroundColor({
                    tabId: tabId,
                    color: '#4facfe'
                });
            } else {
                // Clear badge
                chrome.action.setBadgeText({
                    tabId: tabId,
                    text: ''
                });
            }
            
        } catch (error) {
            console.error('Error checking patterns for tab:', error);
        }
    }
    
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'getSettings':
                    const settings = await this.getSettings();
                    sendResponse({ success: true, data: settings });
                    break;
                    
                case 'updateSettings':
                    await this.updateSettings(message.settings);
                    sendResponse({ success: true });
                    break;
                    
                case 'exportData':
                    const exportData = await this.exportData();
                    sendResponse({ success: true, data: exportData });
                    break;
                    
                case 'importData':
                    await this.importData(message.data);
                    sendResponse({ success: true });
                    break;
                    
                case 'clearAllData':
                    await this.clearAllData();
                    sendResponse({ success: true });
                    break;
                    
                case 'getStatistics':
                    const stats = await this.getStatistics();
                    sendResponse({ success: true, data: stats });
                    break;
                    
                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }
    
    async handleContextMenuClick(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'smart-form-teaching-mode':
                    await chrome.tabs.sendMessage(tab.id, {
                        action: 'toggleTeachingMode',
                        enabled: true
                    });
                    break;
                    
                case 'smart-form-auto-fill':
                    // Open popup for auto-fill options
                    chrome.action.openPopup();
                    break;
                    
                case 'smart-form-settings':
                    // Open settings page
                    chrome.tabs.create({
                        url: chrome.runtime.getURL('settings.html')
                    });
                    break;
            }
        } catch (error) {
            console.error('Error handling context menu click:', error);
        }
    }
    
    onStorageChanged(changes, namespace) {
        if (namespace === 'local') {
            // Handle storage changes
            if (changes.savedPatterns) {
                console.log('Patterns updated');
                this.updateAllTabsBadges();
            }
            
            if (changes.settings) {
                console.log('Settings updated');
                this.broadcastSettingsUpdate(changes.settings.newValue);
            }
        }
    }
    
    async getSettings() {
        const result = await chrome.storage.local.get(['settings']);
        return result.settings || {};
    }
    
    async updateSettings(newSettings) {
        const currentSettings = await this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        await chrome.storage.local.set({ settings: updatedSettings });
    }
    
    async exportData() {
        const result = await chrome.storage.local.get(null);
        return {
            version: chrome.runtime.getManifest().version,
            exportDate: new Date().toISOString(),
            data: result
        };
    }
    
    async importData(importData) {
        if (!importData.data) {
            throw new Error('Invalid import data format');
        }
        
        // Validate and clean import data
        const cleanData = this.validateImportData(importData.data);
        
        // Clear existing data and import new data
        await chrome.storage.local.clear();
        await chrome.storage.local.set(cleanData);
    }
    
    validateImportData(data) {
        const validKeys = ['savedPatterns', 'uploadedData', 'currentPattern', 'settings'];
        const cleanData = {};
        
        validKeys.forEach(key => {
            if (data[key]) {
                cleanData[key] = data[key];
            }
        });
        
        return cleanData;
    }
    
    async clearAllData() {
        await chrome.storage.local.clear();
        
        // Reset to default settings
        const defaultSettings = {
            autoDetectForms: true,
            showNotifications: true,
            defaultWaitTime: 3000,
            autoSubmit: false,
            theme: 'light'
        };
        
        await chrome.storage.local.set({ settings: defaultSettings });
    }
    
    async getStatistics() {
        const result = await chrome.storage.local.get(['savedPatterns', 'uploadedData']);
        
        const stats = {
            totalPatterns: (result.savedPatterns || []).length,
            hasUploadedData: !!result.uploadedData,
            dataRows: result.uploadedData ? result.uploadedData.data.length : 0,
            dataColumns: result.uploadedData ? result.uploadedData.headers.length : 0,
            lastActivity: this.getLastActivityDate(result)
        };
        
        return stats;
    }
    
    getLastActivityDate(data) {
        let lastDate = null;
        
        if (data.uploadedData && data.uploadedData.uploadDate) {
            lastDate = new Date(data.uploadedData.uploadDate);
        }
        
        if (data.savedPatterns) {
            data.savedPatterns.forEach(pattern => {
                if (pattern.saveDate) {
                    const patternDate = new Date(pattern.saveDate);
                    if (!lastDate || patternDate > lastDate) {
                        lastDate = patternDate;
                    }
                }
            });
        }
        
        return lastDate ? lastDate.toISOString() : null;
    }
    
    async updateAllTabsBadges() {
        try {
            const tabs = await chrome.tabs.query({});
            const result = await chrome.storage.local.get(['savedPatterns']);
            const savedPatterns = result.savedPatterns || [];
            
            tabs.forEach(tab => {
                if (tab.url) {
                    const currentUrl = this.getCleanUrl(tab.url);
                    const matchingPatterns = savedPatterns.filter(pattern => pattern.url === currentUrl);
                    
                    if (matchingPatterns.length > 0) {
                        chrome.action.setBadgeText({
                            tabId: tab.id,
                            text: matchingPatterns.length.toString()
                        });
                        chrome.action.setBadgeBackgroundColor({
                            tabId: tab.id,
                            color: '#4facfe'
                        });
                    } else {
                        chrome.action.setBadgeText({
                            tabId: tab.id,
                            text: ''
                        });
                    }
                }
            });
        } catch (error) {
            console.error('Error updating tab badges:', error);
        }
    }
    
    async broadcastSettingsUpdate(newSettings) {
        try {
            const tabs = await chrome.tabs.query({});
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'settingsUpdated',
                    settings: newSettings
                }).catch(() => {
                    // Ignore errors for tabs that don't have content script
                });
            });
        } catch (error) {
            console.error('Error broadcasting settings update:', error);
        }
    }
    
    getCleanUrl(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.origin + urlObj.pathname;
        } catch (error) {
            return url;
        }
    }
    
    showNotification(title, message) {
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: title,
                message: message
            });
        }
    }
    
    migrateData(previousVersion) {
        // Handle data migration between versions
        console.log(`Migrating data from version ${previousVersion}`);
        
        // Add version-specific migration logic here
        // For example:
        // if (previousVersion < '1.1.0') {
        //     // Migrate old format to new format
        // }
    }
}

// Initialize background script
const smartFormBackground = new SmartFormBackground();
