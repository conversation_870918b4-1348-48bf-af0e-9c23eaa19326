// Smart Form Filler - Utility Functions

/**
 * Utility class for common functions
 */
class SmartFormUtils {
    
    /**
     * Parse CSV data
     * @param {string} csvText - Raw CSV text
     * @returns {Array} Parsed data array
     */
    static parseCSV(csvText) {
        console.log('Parsing CSV text...');

        if (!csvText || csvText.trim().length === 0) {
            throw new Error('ملف CSV فارغ');
        }

        // Split lines and filter out empty lines
        const lines = csvText.trim().split(/\r?\n/).filter(line => line.trim().length > 0);
        console.log('CSV lines count:', lines.length);

        if (lines.length < 2) {
            throw new Error('ملف CSV يجب أن يحتوي على رأس وصف واحد على الأقل');
        }

        // Parse headers - handle quoted values
        const headerLine = lines[0];
        console.log('Header line:', headerLine);

        const headers = this.parseCSVLine(headerLine);
        console.log('Parsed headers:', headers);

        if (headers.length === 0) {
            throw new Error('لم يتم العثور على رؤوس أعمدة صحيحة');
        }

        const data = [];

        // Parse data rows
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line.length === 0) continue;

            const values = this.parseCSVLine(line);

            if (values.length > 0) {
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index] || '';
                });
                data.push(row);
            }
        }

        console.log('Parsed CSV data:', { headers, dataCount: data.length });
        return { headers, data };
    }

    /**
     * Parse a single CSV line handling quoted values
     * @param {string} line - CSV line
     * @returns {Array} Array of values
     */
    static parseCSVLine(line) {
        const values = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
            const char = line[i];

            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                values.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }

        // Add the last value
        values.push(current.trim());

        // Clean up values - remove quotes
        return values.map(value => value.replace(/^"|"$/g, '').trim());
    }
    
    /**
     * Parse JSON data
     * @param {string} jsonText - Raw JSON text
     * @returns {Array} Parsed data array
     */
    static parseJSON(jsonText) {
        console.log('Parsing JSON text...');

        if (!jsonText || jsonText.trim().length === 0) {
            throw new Error('ملف JSON فارغ');
        }

        try {
            const parsed = JSON.parse(jsonText);
            console.log('JSON parsed successfully:', typeof parsed);

            let data;

            // Handle different JSON structures
            if (Array.isArray(parsed)) {
                data = parsed;
            } else if (parsed && typeof parsed === 'object') {
                // If it's an object, try to find an array property
                const arrayKeys = Object.keys(parsed).filter(key => Array.isArray(parsed[key]));
                if (arrayKeys.length > 0) {
                    data = parsed[arrayKeys[0]];
                } else {
                    // Convert single object to array
                    data = [parsed];
                }
            } else {
                throw new Error('تنسيق JSON غير صحيح - يجب أن يكون مصفوفة أو كائن');
            }

            if (!Array.isArray(data) || data.length === 0) {
                throw new Error('ملف JSON لا يحتوي على بيانات');
            }

            // Extract headers from first object
            const firstItem = data[0];
            if (!firstItem || typeof firstItem !== 'object') {
                throw new Error('عناصر JSON يجب أن تكون كائنات');
            }

            const headers = Object.keys(firstItem);
            console.log('JSON headers:', headers);
            console.log('JSON data count:', data.length);

            if (headers.length === 0) {
                throw new Error('لم يتم العثور على خصائص في كائنات JSON');
            }

            return { headers, data };

        } catch (error) {
            console.error('Error parsing JSON:', error);
            if (error.message.includes('JSON')) {
                throw new Error('خطأ في تنسيق JSON: ' + error.message);
            }
            throw error;
        }
    }
    
    /**
     * Format file size
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted size string
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * Generate unique ID
     * @returns {string} Unique identifier
     */
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    /**
     * Get current page URL without parameters
     * @returns {string} Clean URL
     */
    static getCurrentPageUrl() {
        return window.location.origin + window.location.pathname;
    }
    
    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {boolean} Is valid email
     */
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    /**
     * Get element selector path
     * @param {Element} element - DOM element
     * @returns {string} CSS selector path
     */
    static getElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        let path = [];
        while (element && element.nodeType === Node.ELEMENT_NODE) {
            let selector = element.nodeName.toLowerCase();
            
            if (element.className) {
                selector += '.' + element.className.trim().split(/\s+/).join('.');
            }
            
            // Add nth-child if needed
            let sibling = element;
            let nth = 1;
            while (sibling = sibling.previousElementSibling) {
                if (sibling.nodeName.toLowerCase() === selector.split('.')[0]) {
                    nth++;
                }
            }
            
            if (nth > 1) {
                selector += `:nth-child(${nth})`;
            }
            
            path.unshift(selector);
            element = element.parentNode;
            
            // Stop at body or if we have a unique selector
            if (element.nodeName.toLowerCase() === 'body' || path.length > 5) {
                break;
            }
        }
        
        return path.join(' > ');
    }
    
    /**
     * Wait for element to appear
     * @param {string} selector - CSS selector
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise<Element>} Promise that resolves with element
     */
    static waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }
            
            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }
    
    /**
     * Simulate user input
     * @param {Element} element - Input element
     * @param {string} value - Value to set
     */
    static simulateInput(element, value) {
        // Focus the element
        element.focus();
        
        // Clear existing value
        element.value = '';
        
        // Set new value
        element.value = value;
        
        // Trigger events
        const events = ['input', 'change', 'blur'];
        events.forEach(eventType => {
            const event = new Event(eventType, { bubbles: true });
            element.dispatchEvent(event);
        });
    }
    
    /**
     * Get form fields from current page
     * @returns {Array} Array of form field objects
     */
    static getFormFields() {
        const fields = [];

        // Comprehensive list of form field selectors
        const selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="number"]',
            'input[type="tel"]',
            'input[type="url"]',
            'input[type="search"]',
            'input[type="date"]',
            'input[type="datetime-local"]',
            'input[type="time"]',
            'input[type="week"]',
            'input[type="month"]',
            'input[type="color"]',
            'input[type="range"]',
            'input[type="file"]',
            'input[type="hidden"]',
            'input:not([type])', // Default input type is text
            'textarea',
            'select',
            'input[type="checkbox"]',
            'input[type="radio"]'
        ];

        console.log('Scanning for form fields...');

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            console.log(`Found ${elements.length} elements for selector: ${selector}`);

            elements.forEach(element => {
                // Include both visible and hidden elements (hidden might be important for forms)
                const isVisible = element.offsetParent !== null;
                const isHidden = element.type === 'hidden';

                if (isVisible || isHidden) {
                    const fieldInfo = {
                        element: element,
                        type: element.type || element.tagName.toLowerCase(),
                        name: element.name || element.id || '',
                        placeholder: element.placeholder || '',
                        selector: this.getElementSelector(element),
                        visible: isVisible,
                        label: this.getFieldLabel(element)
                    };

                    fields.push(fieldInfo);
                }
            });
        });

        console.log(`Total form fields found: ${fields.length}`);
        return fields;
    }

    /**
     * Get label text for a form field
     * @param {Element} element - Form field element
     * @returns {string} Label text
     */
    static getFieldLabel(element) {
        // Try to find associated label
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) {
                return label.textContent.trim();
            }
        }

        // Try to find parent label
        const parentLabel = element.closest('label');
        if (parentLabel) {
            return parentLabel.textContent.trim();
        }

        // Try to find nearby text
        const placeholder = element.placeholder;
        if (placeholder) {
            return placeholder;
        }

        // Try to find previous sibling text
        let prev = element.previousElementSibling;
        while (prev) {
            if (prev.textContent && prev.textContent.trim()) {
                return prev.textContent.trim();
            }
            prev = prev.previousElementSibling;
        }

        return element.name || element.id || 'حقل غير محدد';
    }
    
    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, info)
     */
    static showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `smart-form-notification ${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            zIndex: '10000',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            transition: 'all 0.3s ease'
        });
        
        // Set background color based on type
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            info: '#17a2b8',
            warning: '#ffc107'
        };
        notification.style.backgroundColor = colors[type] || colors.info;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * Debounce function
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartFormUtils;
}
