// Smart Form Filler - Content Script

class SmartFormContent {
    constructor() {
        this.teachingMode = false;
        this.autoDetectionMode = false;
        this.currentPattern = null;
        this.fieldMappings = {};
        this.contextMenu = null;
        this.selectedElement = null;
        this.availableColumns = [];
        this.recordedSteps = [];
        this.currentStep = 0;
        this.isRecording = false;
        this.modalObserver = null;
        this.dropdownOptions = new Map();
        this.buttonSequence = [];
        this.currentDataRow = 0;

        this.init();
    }
    
    init() {
        this.setupMessageListener();
        this.loadCurrentPattern();
        this.checkForExistingPatterns();
        this.setupModalObserver();
        this.setupButtonDetection();
        this.setupDropdownDetection();
        this.setupAutoDetection();
    }
    
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            switch (message.action) {
                case 'toggleTeachingMode':
                    this.toggleTeachingMode(message.enabled);
                    break;
                case 'toggleAutoDetection':
                    this.toggleAutoDetection(message.enabled);
                    break;
                case 'startRecording':
                    this.startRecording();
                    break;
                case 'stopRecording':
                    this.stopRecording();
                    break;
                case 'playRecording':
                    this.playRecording(message.data);
                    break;
                case 'startSmartAutoFill':
                    this.startSmartAutoFill(message.data, message.rowIndex, message.selectedFields);
                    break;
                case 'enableSmartSystem':
                    this.enableSmartSystem(message.dataViewerTabId);
                    break;
                case 'enableSmartLearningSystem':
                    this.enableSmartLearningSystem(message.dataViewerTabId, message.currentData);
                    break;
                case 'activateSimpleSystem':
                    this.activateSimpleSystem(message.dataViewerTabId);
                    break;
                case 'applySimpleMapping':
                    this.applySimpleMapping(message);
                    break;
                case 'setupRightClickSystem':
                    this.setupRightClickSystem(message.dataViewerTabId);
                    break;
                case 'applyFieldMapping':
                    this.applyFieldMapping(message.mapping, message.value);
                    break;
                case 'applyFieldMappingWithData':
                    this.applyFieldMappingWithData(message.mapping, message.value, message.rowData, message.rowIndex);
                    break;
                case 'receiveCurrentRowData':
                    this.receiveCurrentRowData(message.rowData, message.rowIndex, message.columns, message.isNewRow);
                    break;
                case 'noDataAvailable':
                    this.handleNoDataAvailable();
                    break;
                case 'disableLearningMode':
                    this.disableLearningMode();
                    break;
                case 'fillNextRow':
                    this.fillNextRow(message.rowData, message.rowIndex);
                    break;
                case 'allRowsCompleted':
                    this.handleAllRowsCompleted();
                    break;
                case 'startAutoFill':
                    this.startAutoFill(message.data, message.pattern, message.waitTime);
                    break;
                case 'clearPattern':
                    this.clearPattern();
                    break;
                case 'getPageInfo':
                    sendResponse({
                        url: SmartFormUtils.getCurrentPageUrl(),
                        title: document.title,
                        fields: SmartFormUtils.getFormFields()
                    });
                    break;
            }
        });
    }
    
    async loadCurrentPattern() {
        try {
            console.log('Loading current pattern and data...');
            const result = await chrome.storage.local.get(['currentPattern', 'uploadedData']);
            console.log('Storage result:', result);

            if (result.currentPattern && result.currentPattern.url === SmartFormUtils.getCurrentPageUrl()) {
                this.currentPattern = result.currentPattern;
                this.fieldMappings = result.currentPattern.fieldMappings || {};
                console.log('Loaded current pattern:', this.currentPattern);
            }

            if (result.uploadedData) {
                this.availableColumns = result.uploadedData.headers || [];
                console.log('Loaded available columns:', this.availableColumns);
            } else {
                console.log('No uploaded data found');
                this.availableColumns = [];
            }

            // Also listen for storage changes to update columns in real-time
            chrome.storage.onChanged.addListener((changes, namespace) => {
                if (namespace === 'local' && changes.uploadedData) {
                    console.log('Uploaded data changed, updating columns...');
                    const newData = changes.uploadedData.newValue;
                    if (newData && newData.headers) {
                        this.availableColumns = newData.headers;
                        console.log('Updated available columns:', this.availableColumns);
                    }
                }
            });

        } catch (error) {
            console.error('Error loading current pattern:', error);
        }
    }
    
    async checkForExistingPatterns() {
        try {
            const result = await chrome.storage.local.get(['savedPatterns']);
            const savedPatterns = result.savedPatterns || [];
            
            const currentUrl = SmartFormUtils.getCurrentPageUrl();
            const matchingPattern = savedPatterns.find(pattern => pattern.url === currentUrl);
            
            if (matchingPattern) {
                this.showPatternNotification(matchingPattern);
            }
            
        } catch (error) {
            console.error('Error checking for existing patterns:', error);
        }
    }
    
    showPatternNotification(pattern) {
        // Create notification for existing pattern
        const notification = document.createElement('div');
        notification.id = 'smart-form-pattern-notification';
        notification.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
                max-width: 300px;
                direction: rtl;
            ">
                <div style="font-weight: bold; margin-bottom: 8px;">
                    🎯 نمط محفوظ متاح
                </div>
                <div style="margin-bottom: 10px;">
                    ${pattern.name}
                </div>
                <div style="display: flex; gap: 10px;">
                    <button id="load-pattern-btn" style="
                        background: white;
                        color: #4facfe;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">تحميل النمط</button>
                    <button id="dismiss-notification-btn" style="
                        background: rgba(255,255,255,0.2);
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">إغلاق</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Add event listeners
        document.getElementById('load-pattern-btn').addEventListener('click', () => {
            this.loadPattern(pattern);
            notification.remove();
        });
        
        document.getElementById('dismiss-notification-btn').addEventListener('click', () => {
            notification.remove();
        });
        
        // Auto remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }
    
    loadPattern(pattern) {
        this.currentPattern = pattern;
        this.fieldMappings = pattern.fieldMappings || {};
        
        // Save as current pattern
        chrome.storage.local.set({ currentPattern: pattern });
        
        SmartFormUtils.showNotification('تم تحميل النمط بنجاح', 'success');
        
        // Notify popup
        chrome.runtime.sendMessage({
            action: 'patternUpdated',
            pattern: pattern
        });
    }
    
    toggleTeachingMode(enabled) {
        this.teachingMode = enabled;
        
        if (enabled) {
            this.enableTeachingMode();
        } else {
            this.disableTeachingMode();
        }
    }
    
    async enableTeachingMode() {
        console.log('Enabling teaching mode...');

        // Reload data to ensure we have latest columns
        await this.loadCurrentPattern();

        // Add visual indicators
        this.addTeachingModeStyles();

        // Add event listeners for right-click
        document.addEventListener('contextmenu', this.handleRightClick.bind(this));
        document.addEventListener('click', this.handleElementClick.bind(this));

        // Initialize current pattern if not exists
        if (!this.currentPattern) {
            this.currentPattern = {
                url: SmartFormUtils.getCurrentPageUrl(),
                title: document.title,
                fieldMappings: {},
                submitButton: null,
                waitTime: 3000
            };
        }

        // Check if we have available columns
        if (this.availableColumns.length === 0) {
            SmartFormUtils.showNotification('تحذير: لا توجد أعمدة متاحة. تأكد من رفع ملف البيانات أولاً', 'warning');
        } else {
            SmartFormUtils.showNotification(`وضع التعليم مفعل - انقر بالزر الأيمن على الحقول (${this.availableColumns.length} عمود متاح)`, 'info');
        }

        console.log('Teaching mode enabled with columns:', this.availableColumns);
    }
    
    disableTeachingMode() {
        // Remove visual indicators
        this.removeTeachingModeStyles();
        
        // Remove event listeners
        document.removeEventListener('contextmenu', this.handleRightClick.bind(this));
        document.removeEventListener('click', this.handleElementClick.bind(this));
        
        // Hide context menu if visible
        this.hideContextMenu();
        
        SmartFormUtils.showNotification('تم إلغاء وضع التعليم', 'info');
    }
    
    addTeachingModeStyles() {
        const style = document.createElement('style');
        style.id = 'smart-form-teaching-styles';
        style.textContent = `
            .smart-form-highlightable {
                outline: 2px dashed #4facfe !important;
                outline-offset: 2px !important;
                cursor: pointer !important;
            }
            .smart-form-highlightable:hover {
                outline-color: #00f2fe !important;
                background-color: rgba(79, 172, 254, 0.1) !important;
            }
            .smart-form-mapped {
                outline: 2px solid #28a745 !important;
                outline-offset: 2px !important;
            }
        `;
        document.head.appendChild(style);
        
        // Add highlighting to form fields
        this.highlightFormFields();
    }
    
    removeTeachingModeStyles() {
        const style = document.getElementById('smart-form-teaching-styles');
        if (style) {
            style.remove();
        }
        
        // Remove highlighting classes
        document.querySelectorAll('.smart-form-highlightable, .smart-form-mapped').forEach(el => {
            el.classList.remove('smart-form-highlightable', 'smart-form-mapped');
        });
    }
    
    highlightFormFields() {
        console.log('Highlighting form fields...');

        // Get all form fields
        const fields = SmartFormUtils.getFormFields();
        console.log('Found form fields:', fields.length);

        fields.forEach(field => {
            field.element.classList.add('smart-form-highlightable');

            // Check if field is already mapped
            const selector = SmartFormUtils.getElementSelector(field.element);
            if (this.fieldMappings[selector]) {
                field.element.classList.add('smart-form-mapped');
            }
        });

        // Highlight ALL interactive elements
        const interactiveSelectors = [
            'button',
            'input[type="submit"]',
            'input[type="button"]',
            'input[type="reset"]',
            'a[href]',
            '[onclick]',
            '[role="button"]',
            '.btn',
            '.button'
        ];

        interactiveSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            console.log(`Found ${elements.length} elements for selector: ${selector}`);

            elements.forEach(element => {
                // Only highlight visible elements
                if (element.offsetParent !== null &&
                    !element.classList.contains('smart-form-highlightable')) {
                    element.classList.add('smart-form-highlightable');
                    element.setAttribute('data-smart-form-type', 'interactive');
                }
            });
        });

        console.log('Form field highlighting complete');
    }
    
    handleRightClick(event) {
        console.log('Right click detected, teaching mode:', this.teachingMode);

        if (!this.teachingMode) {
            console.log('Teaching mode not active, ignoring right click');
            return;
        }

        const target = event.target;
        console.log('Right click target:', target.tagName, target.type, target.className);

        const isFormField = this.isFormField(target);
        const isButton = this.isButton(target);
        const isInteractive = target.classList.contains('smart-form-highlightable');

        console.log('Element classification:', {
            isFormField,
            isButton,
            isInteractive,
            hasColumns: this.availableColumns.length
        });

        if (isFormField || isButton || isInteractive) {
            event.preventDefault();
            event.stopPropagation();

            this.selectedElement = target;
            console.log('Selected element for context menu:', target);

            // Check if we have available columns
            if (this.availableColumns.length === 0) {
                console.warn('No available columns found');
                SmartFormUtils.showNotification('لا توجد أعمدة متاحة. تأكد من رفع ملف البيانات أولاً', 'warning');
                return;
            }

            this.showContextMenu(event.clientX, event.clientY, isButton);
        } else {
            console.log('Element not eligible for context menu');
        }
    }
    
    handleElementClick(event) {
        if (!this.teachingMode) return;
        
        // Hide context menu on click elsewhere
        if (!event.target.closest('#smart-form-context-menu')) {
            this.hideContextMenu();
        }
    }
    
    isFormField(element) {
        const formFieldTypes = ['input', 'textarea', 'select'];
        const tagName = element.tagName.toLowerCase();

        // Check if it's a form field by tag name
        if (formFieldTypes.includes(tagName)) {
            return true;
        }

        // Check if it has form field attributes
        if (element.hasAttribute('name') || element.hasAttribute('data-name')) {
            return true;
        }

        // Check if it's inside a form
        if (element.closest('form')) {
            return true;
        }

        return false;
    }

    isButton(element) {
        const tagName = element.tagName.toLowerCase();

        // Direct button elements
        if (tagName === 'button') {
            return true;
        }

        // Input buttons
        if (tagName === 'input') {
            const buttonTypes = ['submit', 'button', 'reset'];
            return buttonTypes.includes(element.type);
        }

        // Elements with button role
        if (element.getAttribute('role') === 'button') {
            return true;
        }

        // Elements with button classes
        const buttonClasses = ['btn', 'button', 'submit', 'save', 'create', 'add'];
        const className = element.className.toLowerCase();
        if (buttonClasses.some(cls => className.includes(cls))) {
            return true;
        }

        // Elements with click handlers that look like buttons
        if (element.hasAttribute('onclick') || element.style.cursor === 'pointer') {
            return true;
        }

        return false;
    }
    
    showContextMenu(x, y, isButton = false) {
        console.log('Showing context menu at:', x, y, 'isButton:', isButton);
        console.log('Available columns:', this.availableColumns);

        this.hideContextMenu(); // Hide existing menu

        const menu = document.createElement('div');
        menu.id = 'smart-form-context-menu';
        menu.style.cssText = `
            position: fixed;
            top: ${y}px;
            left: ${x}px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            z-index: 10001;
            min-width: 250px;
            max-width: 350px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            direction: rtl;
            overflow: hidden;
        `;

        if (isButton) {
            menu.innerHTML = `
                <div class="menu-header" style="
                    padding: 12px 16px;
                    border-bottom: 1px solid #eee;
                    font-weight: bold;
                    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
                    color: #333;
                ">
                    🎯 تحديد كزر إرسال
                </div>
                <div class="menu-item" data-action="set-submit-button" style="
                    padding: 12px 16px;
                    cursor: pointer;
                    border-bottom: 1px solid #eee;
                    transition: background 0.2s;
                ">
                    🎯 تعيين كزر الإرسال
                </div>
                <div class="menu-item" data-action="set-wait-time" style="
                    padding: 12px 16px;
                    cursor: pointer;
                    transition: background 0.2s;
                ">
                    ⏱️ تحديد مدة الانتظار
                </div>
            `;
        } else {
            // Ensure we have columns to show
            if (this.availableColumns.length === 0) {
                console.error('No available columns to show in context menu');
                SmartFormUtils.showNotification('لا توجد أعمدة متاحة. تأكد من رفع ملف البيانات أولاً', 'error');
                return;
            }

            const columnsHtml = this.availableColumns.map(column => `
                <div class="menu-item" data-column="${column}" style="
                    padding: 12px 16px;
                    cursor: pointer;
                    border-bottom: 1px solid #eee;
                    transition: background 0.2s;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                ">
                    <span style="color: #4facfe;">📊</span>
                    <span>${column}</span>
                </div>
            `).join('');

            menu.innerHTML = `
                <div class="menu-header" style="
                    padding: 12px 16px;
                    border-bottom: 1px solid #eee;
                    font-weight: bold;
                    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
                    color: #333;
                ">
                    📋 ربط الحقل بعمود (${this.availableColumns.length} عمود متاح)
                </div>
                ${columnsHtml}
                <div class="menu-item" data-action="set-fixed-value" style="
                    padding: 12px 16px;
                    cursor: pointer;
                    border-bottom: 1px solid #eee;
                    transition: background 0.2s;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                ">
                    <span style="color: #28a745;">📝</span>
                    <span>قيمة ثابتة</span>
                </div>
                <div class="menu-item" data-action="remove-mapping" style="
                    padding: 12px 16px;
                    cursor: pointer;
                    color: #dc3545;
                    transition: background 0.2s;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                ">
                    <span>🗑️</span>
                    <span>إزالة الربط</span>
                </div>
            `;
        }
        
        // Add hover effects
        const style = document.createElement('style');
        style.textContent = `
            #smart-form-context-menu .menu-item:hover {
                background-color: #f8f9fa;
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(menu);
        this.contextMenu = menu;
        
        // Add event listeners
        menu.addEventListener('click', (e) => {
            const target = e.target.closest('.menu-item');
            if (target) {
                this.handleMenuItemClick(target);
            }
        });
        
        // Position menu within viewport
        const rect = menu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            menu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            menu.style.top = (y - rect.height) + 'px';
        }
    }
    
    hideContextMenu() {
        if (this.contextMenu) {
            this.contextMenu.remove();
            this.contextMenu = null;
        }
    }
    
    handleMenuItemClick(menuItem) {
        const action = menuItem.dataset.action;
        const column = menuItem.dataset.column;
        
        if (!this.selectedElement) return;
        
        const selector = SmartFormUtils.getElementSelector(this.selectedElement);
        
        if (action === 'set-submit-button') {
            this.currentPattern.submitButton = selector;
            SmartFormUtils.showNotification('تم تعيين زر الإرسال', 'success');
            
        } else if (action === 'set-wait-time') {
            const waitTime = prompt('أدخل مدة الانتظار بالثواني:', '3');
            if (waitTime && !isNaN(waitTime)) {
                this.currentPattern.waitTime = parseInt(waitTime) * 1000;
                SmartFormUtils.showNotification(`تم تحديد مدة الانتظار: ${waitTime} ثانية`, 'success');
            }
            
        } else if (action === 'set-fixed-value') {
            const fixedValue = prompt('أدخل القيمة الثابتة:');
            if (fixedValue !== null) {
                this.fieldMappings[selector] = {
                    type: 'fixed',
                    value: fixedValue
                };
                this.selectedElement.classList.add('smart-form-mapped');
                SmartFormUtils.showNotification('تم تعيين قيمة ثابتة', 'success');
            }
            
        } else if (action === 'remove-mapping') {
            delete this.fieldMappings[selector];
            this.selectedElement.classList.remove('smart-form-mapped');
            SmartFormUtils.showNotification('تم إزالة الربط', 'info');
            
        } else if (column) {
            this.fieldMappings[selector] = {
                type: 'column',
                column: column
            };
            this.selectedElement.classList.add('smart-form-mapped');
            SmartFormUtils.showNotification(`تم ربط الحقل بعمود: ${column}`, 'success');
        }
        
        // Update pattern
        this.currentPattern.fieldMappings = this.fieldMappings;
        
        // Notify popup
        chrome.runtime.sendMessage({
            action: 'patternUpdated',
            pattern: this.currentPattern
        });
        
        this.hideContextMenu();
    }
    
    async startAutoFill(data, pattern, waitTime) {
        try {
            SmartFormUtils.showNotification('بدء التعبئة التلقائية...', 'info');
            
            // Fill form fields
            for (const [selector, mapping] of Object.entries(pattern.fieldMappings)) {
                try {
                    const element = document.querySelector(selector);
                    if (element) {
                        let value = '';
                        
                        if (mapping.type === 'column' && data[mapping.column]) {
                            value = data[mapping.column];
                        } else if (mapping.type === 'fixed') {
                            value = mapping.value;
                        }
                        
                        if (value) {
                            this.fillField(element, value);
                            await this.delay(100); // Small delay between fields
                        }
                    }
                } catch (error) {
                    console.error(`Error filling field ${selector}:`, error);
                }
            }
            
            SmartFormUtils.showNotification('تم الانتهاء من التعبئة', 'success');
            
            // Auto-submit if submit button is defined and wait time is set
            if (pattern.submitButton && waitTime > 0) {
                setTimeout(() => {
                    this.submitForm(pattern.submitButton);
                }, waitTime);
            }
            
            // Notify popup
            chrome.runtime.sendMessage({
                action: 'autoFillComplete'
            });
            
        } catch (error) {
            console.error('Error during auto fill:', error);
            SmartFormUtils.showNotification('خطأ في التعبئة التلقائية', 'error');
            
            chrome.runtime.sendMessage({
                action: 'autoFillError',
                error: error.message
            });
        }
    }
    
    fillField(element, value) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type ? element.type.toLowerCase() : '';
        
        if (tagName === 'select') {
            // Handle select dropdown
            const option = Array.from(element.options).find(opt => 
                opt.text.includes(value) || opt.value === value
            );
            if (option) {
                element.value = option.value;
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
            
        } else if (type === 'checkbox' || type === 'radio') {
            // Handle checkboxes and radio buttons
            const shouldCheck = ['true', '1', 'yes', 'نعم', 'صحيح'].includes(value.toLowerCase());
            element.checked = shouldCheck;
            element.dispatchEvent(new Event('change', { bubbles: true }));
            
        } else {
            // Handle text inputs and textareas
            SmartFormUtils.simulateInput(element, value);
        }
    }
    
    submitForm(submitButtonSelector) {
        try {
            const submitButton = document.querySelector(submitButtonSelector);
            if (submitButton) {
                SmartFormUtils.showNotification('جاري إرسال النموذج...', 'info');
                submitButton.click();
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            SmartFormUtils.showNotification('خطأ في إرسال النموذج', 'error');
        }
    }
    
    clearPattern() {
        this.currentPattern = null;
        this.fieldMappings = {};
        
        // Remove mapped classes
        document.querySelectorAll('.smart-form-mapped').forEach(el => {
            el.classList.remove('smart-form-mapped');
        });
        
        SmartFormUtils.showNotification('تم مسح النمط', 'info');
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ===== نظام التعرف التلقائي على العناصر =====

    setupAutoDetection() {
        this.detectButtons();
        this.detectModals();
        this.detectDropdowns();
        this.detectRadioButtons();
    }

    toggleAutoDetection(enabled) {
        this.autoDetectionMode = enabled;
        if (enabled) {
            this.highlightInteractiveElements();
            SmartFormUtils.showNotification('تم تفعيل وضع التعرف التلقائي', 'success');
        } else {
            this.removeHighlights();
            SmartFormUtils.showNotification('تم إيقاف وضع التعرف التلقائي', 'info');
        }
    }

    highlightInteractiveElements() {
        // إضافة تمييز للأزرار مع تسميات
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], .btn');
        buttons.forEach(btn => {
            btn.style.outline = '3px solid #ff6b6b';
            btn.style.outlineOffset = '2px';
            btn.setAttribute('data-smart-detected', 'button');
            this.addElementLabel(btn, '🔘 زر', '#ff6b6b');
        });

        // إضافة تمييز للقوائم المنسدلة
        const dropdowns = document.querySelectorAll('select, .dropdown');
        dropdowns.forEach(dd => {
            dd.style.outline = '3px solid #4ecdc4';
            dd.style.outlineOffset = '2px';
            dd.setAttribute('data-smart-detected', 'dropdown');
            this.addElementLabel(dd, '📋 قائمة', '#4ecdc4');
        });

        // إضافة تمييز لأزرار الراديو
        const radios = document.querySelectorAll('input[type="radio"]');
        radios.forEach(radio => {
            radio.parentElement.style.outline = '3px solid #45b7d1';
            radio.parentElement.style.outlineOffset = '2px';
            radio.setAttribute('data-smart-detected', 'radio');
            this.addElementLabel(radio.parentElement, '🔘 راديو', '#45b7d1');
        });

        // إضافة تمييز خاص لأزرار الإنشاء
        const createButtons = this.findElementsByText('');
        createButtons.forEach(btn => {
            btn.style.outline = '4px solid #28a745';
            btn.style.outlineOffset = '3px';
            btn.setAttribute('data-smart-detected', 'create-button');
            this.addElementLabel(btn, '🔴 إنشاء', '#28a745');
        });

        // إضافة تمييز للحقول
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="number"], textarea');
        inputs.forEach(input => {
            input.style.outline = '2px solid #ffc107';
            input.style.outlineOffset = '1px';
            input.setAttribute('data-smart-detected', 'input');
            this.addElementLabel(input, '📝 حقل', '#ffc107');
        });
    }

    addElementLabel(element, text, color) {
        // إزالة التسمية السابقة إن وجدت
        const existingLabel = element.querySelector('.smart-element-label');
        if (existingLabel) {
            existingLabel.remove();
        }

        const label = document.createElement('div');
        label.className = 'smart-element-label';
        label.style.cssText = `
            position: absolute;
            top: -25px;
            right: -5px;
            background: ${color};
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            z-index: 9999;
            pointer-events: none;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            animation: labelPulse 2s infinite;
        `;
        label.textContent = text;

        // إضافة الأنيميشن
        if (!document.getElementById('label-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'label-animation-styles';
            style.textContent = `
                @keyframes labelPulse {
                    0%, 100% { opacity: 0.8; transform: scale(1); }
                    50% { opacity: 1; transform: scale(1.05); }
                }
            `;
            document.head.appendChild(style);
        }

        // تأكد من أن العنصر له position relative
        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(label);
    }

    removeHighlights() {
        document.querySelectorAll('[data-smart-detected]').forEach(el => {
            el.style.outline = '';
            el.style.outlineOffset = '';
            el.removeAttribute('data-smart-detected');

            // إزالة التسميات
            const labels = el.querySelectorAll('.smart-element-label');
            labels.forEach(label => label.remove());
        });
    }

    // ===== نظام تسجيل الخطوات =====

    startRecording() {
        this.isRecording = true;
        this.recordedSteps = [];
        this.currentStep = 0;
        SmartFormUtils.showNotification('بدء تسجيل الخطوات...', 'success');
        this.setupRecordingListeners();
        this.showRecordingIndicator();
    }

    stopRecording() {
        this.isRecording = false;
        this.removeRecordingListeners();
        SmartFormUtils.showNotification(`تم تسجيل ${this.recordedSteps.length} خطوة`, 'success');
        this.saveRecordedSteps();
        this.hideRecordingIndicator();
    }

    setupRecordingListeners() {
        // استخدام مراجع ثابتة للدوال
        this.boundRecordClick = this.recordClick.bind(this);
        this.boundRecordChange = this.recordChange.bind(this);
        this.boundRecordInput = this.recordInput.bind(this);

        document.addEventListener('click', this.boundRecordClick, true);
        document.addEventListener('change', this.boundRecordChange, true);
        document.addEventListener('input', this.boundRecordInput, true);
    }

    removeRecordingListeners() {
        if (this.boundRecordClick) {
            document.removeEventListener('click', this.boundRecordClick, true);
        }
        if (this.boundRecordChange) {
            document.removeEventListener('change', this.boundRecordChange, true);
        }
        if (this.boundRecordInput) {
            document.removeEventListener('input', this.boundRecordInput, true);
        }
    }

    recordClick(event) {
        if (!this.isRecording) return;

        const element = event.target;

        // إضافة تأثير بصري فوري للنقرة
        this.showClickIndicator(event.clientX, event.clientY, element);

        const step = {
            type: 'click',
            selector: this.generateSelector(element),
            elementType: element.tagName.toLowerCase(),
            text: element.textContent?.trim() || element.value || '',
            timestamp: Date.now(),
            position: { x: event.clientX, y: event.clientY }
        };

        // تحديد نوع العنصر المحدد
        if (element.type === 'button' || element.tagName === 'BUTTON') {
            step.elementType = 'button';
            step.buttonText = element.textContent?.trim();
            step.isCreateButton = this.isCreateButton(element);
        } else if (element.type === 'radio') {
            step.elementType = 'radio';
            step.value = element.value;
            step.name = element.name;
        } else if (element.classList.contains('dropdown') || element.tagName === 'SELECT') {
            step.elementType = 'dropdown';
        }

        this.recordStep(step);
        this.highlightRecordedElement(element);

        console.log('تم تسجيل نقرة:', step);

        // إذا كان زر إنشاء، انتظر النافذة المنبثقة وفعل التعلم الذكي فيها
        if (step.isCreateButton) {
            setTimeout(() => {
                this.handleModalForRecording();
            }, 500); // انتظار قصير لظهور النافذة المنبثقة
        }

        // عدم إيقاف التسجيل - الاستمرار في التسجيل
        // لا نوقف التسجيل هنا مطلقاً
    }

    recordChange(event) {
        if (!this.isRecording) return;

        const element = event.target;
        const step = {
            type: 'change',
            selector: this.generateSelector(element),
            elementType: element.tagName.toLowerCase(),
            value: element.value,
            timestamp: Date.now()
        };

        if (element.tagName === 'SELECT') {
            step.elementType = 'select';
            step.selectedText = element.options[element.selectedIndex]?.text;
            step.selectedValue = element.value;
            step.allOptions = Array.from(element.options).map(opt => ({
                value: opt.value,
                text: opt.text
            }));
        }

        this.recordStep(step);
        console.log('تم تسجيل تغيير:', step);
    }

    recordInput(event) {
        if (!this.isRecording) return;

        const element = event.target;
        const step = {
            type: 'input',
            selector: this.generateSelector(element),
            elementType: element.tagName.toLowerCase(),
            value: element.value,
            placeholder: element.placeholder || '',
            timestamp: Date.now()
        };

        this.recordStep(step);
    }

    generateSelector(element) {
        // إنشاء محدد فريد للعنصر
        if (element.id) {
            return `#${element.id}`;
        }

        if (element.name) {
            return `[name="${element.name}"]`;
        }

        if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
                return `.${classes.join('.')}`;
            }
        }

        // استخدام مسار العنصر
        const path = [];
        let current = element;
        while (current && current !== document.body) {
            let selector = current.tagName.toLowerCase();
            if (current.id) {
                selector += `#${current.id}`;
                path.unshift(selector);
                break;
            }
            if (current.className) {
                const classes = current.className.split(' ').filter(c => c.trim());
                if (classes.length > 0) {
                    selector += `.${classes[0]}`;
                }
            }
            path.unshift(selector);
            current = current.parentElement;
        }

        return path.join(' > ');
    }

    highlightRecordedElement(element) {
        element.style.boxShadow = '0 0 10px #ff6b6b';
        setTimeout(() => {
            element.style.boxShadow = '';
        }, 1000);
    }

    showClickIndicator(x, y, element) {
        // إنشاء مؤشر النقرة
        const indicator = document.createElement('div');
        indicator.style.cssText = `
            position: fixed;
            left: ${x - 25}px;
            top: ${y - 25}px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            pointer-events: none;
            z-index: 10000;
            animation: clickRipple 0.6s ease-out;
            border: 3px solid #ff6b6b;
            background: rgba(255, 107, 107, 0.2);
        `;

        // تحديد لون المؤشر حسب نوع العنصر
        const elementType = this.getElementType(element);
        const colors = {
            'create-button': '#ff6b6b',    // أحمر للأزرار الإنشاء
            'submit-button': '#28a745',    // أخضر لأزرار الإرسال
            'dropdown': '#17a2b8',         // أزرق للقوائم المنسدلة
            'radio': '#6f42c1',            // بنفسجي لأزرار الراديو
            'input': '#ffc107',            // أصفر للحقول
            'default': '#007bff'           // أزرق افتراضي
        };

        const color = colors[elementType] || colors['default'];
        indicator.style.borderColor = color;
        indicator.style.background = `${color}33`; // شفافية 20%

        // إضافة نص يوضح نوع العنصر
        const label = document.createElement('div');
        label.style.cssText = `
            position: fixed;
            left: ${x + 30}px;
            top: ${y - 10}px;
            background: ${color};
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            pointer-events: none;
            z-index: 10001;
            animation: fadeInOut 1s ease-out;
            white-space: nowrap;
        `;
        label.textContent = this.getElementTypeLabel(elementType);

        // إضافة الأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes clickRipple {
                0% {
                    transform: scale(0);
                    opacity: 1;
                }
                100% {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translateY(10px); }
                50% { opacity: 1; transform: translateY(0); }
                100% { opacity: 0; transform: translateY(-10px); }
            }
        `;

        if (!document.getElementById('click-indicator-styles')) {
            style.id = 'click-indicator-styles';
            document.head.appendChild(style);
        }

        document.body.appendChild(indicator);
        document.body.appendChild(label);

        // إزالة المؤشر بعد انتهاء الأنيميشن
        setTimeout(() => {
            if (indicator.parentNode) indicator.remove();
            if (label.parentNode) label.remove();
        }, 1000);

        // إضافة صوت (اختياري)
        this.playClickSound();
    }

    getElementType(element) {
        if (this.isCreateButton(element)) return 'create-button';
        if (element.type === 'submit' || element.classList.contains('submit-button')) return 'submit-button';
        if (element.tagName === 'SELECT' || element.classList.contains('dropdown')) return 'dropdown';
        if (element.type === 'radio') return 'radio';
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') return 'input';
        if (element.tagName === 'BUTTON' || element.type === 'button') return 'button';
        return 'default';
    }

    getElementTypeLabel(elementType) {
        const labels = {
            'create-button': '🔴 زر إنشاء',
            'submit-button': '✅ زر إرسال',
            'dropdown': '📋 قائمة منسدلة',
            'radio': '🔘 زر راديو',
            'input': '📝 حقل إدخال',
            'button': '🔘 زر',
            'default': '👆 نقرة'
        };
        return labels[elementType] || labels['default'];
    }

    playClickSound() {
        // إنشاء صوت نقرة بسيط
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (e) {
            // تجاهل الأخطاء في حالة عدم دعم الصوت
        }
    }

    saveRecordedSteps() {
        const recordingData = {
            steps: this.recordedSteps,
            timestamp: Date.now(),
            url: window.location.href,
            title: document.title,
            totalSteps: this.recordedSteps.length,
            fieldMappings: this.fieldMappings || {},
            recordingName: this.generateRecordingName()
        };

        // حفظ في التخزين المحلي
        chrome.storage.local.set({
            currentRecording: recordingData,
            recordingTimestamp: Date.now()
        });

        // حفظ في قائمة التسجيلات المحفوظة
        this.saveToRecordingsList(recordingData);

        // إرسال البيانات للـ popup
        chrome.runtime.sendMessage({
            action: 'recordingSaved',
            recording: recordingData
        });

        console.log('تم حفظ التسجيل:', recordingData);
    }

    generateRecordingName() {
        const now = new Date();
        const date = now.toLocaleDateString('ar-SA');
        const time = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        return `تسجيل ${date} - ${time}`;
    }

    async saveToRecordingsList(recordingData) {
        try {
            const result = await chrome.storage.local.get(['savedRecordings']);
            const savedRecordings = result.savedRecordings || [];

            // إضافة التسجيل الجديد
            savedRecordings.unshift(recordingData);

            // الاحتفاظ بآخر 10 تسجيلات فقط
            if (savedRecordings.length > 10) {
                savedRecordings.splice(10);
            }

            await chrome.storage.local.set({ savedRecordings });

        } catch (error) {
            console.error('خطأ في حفظ قائمة التسجيلات:', error);
        }
    }

    async playRecording(data) {
        if (!this.recordedSteps || this.recordedSteps.length === 0) {
            SmartFormUtils.showNotification('لا توجد خطوات مسجلة للتشغيل', 'warning');
            return;
        }

        SmartFormUtils.showNotification('بدء تشغيل الخطوات المسجلة...', 'info');

        for (let i = 0; i < this.recordedSteps.length; i++) {
            const step = this.recordedSteps[i];
            await this.executeStep(step, data);
            await this.delay(500); // انتظار بين الخطوات
        }

        SmartFormUtils.showNotification('تم الانتهاء من تشغيل الخطوات', 'success');
    }

    async executeStep(step, data) {
        try {
            const element = document.querySelector(step.selector);
            if (!element) {
                console.warn('لم يتم العثور على العنصر:', step.selector);
                return;
            }

            switch (step.type) {
                case 'click':
                    if (step.isCreateButton) {
                        element.click();
                        await this.waitForModal();
                    } else {
                        element.click();
                    }
                    break;

                case 'input':
                    if (data && data[this.currentDataRow]) {
                        const value = this.getValueForField(step, data[this.currentDataRow]);
                        if (value) {
                            SmartFormUtils.simulateInput(element, value);
                        }
                    }
                    break;

                case 'change':
                    if (step.elementType === 'select') {
                        await this.handleDropdownSelection(element, step, data);
                    }
                    break;
            }

        } catch (error) {
            console.error('خطأ في تنفيذ الخطوة:', error);
        }
    }

    // ===== نظام التعرف على الأزرار =====

    setupButtonDetection() {
        this.detectCreateButtons();
        this.detectSubmitButtons();
        this.detectActionButtons();
    }

    detectCreateButtons() {
        const createSelectors = [
            'button:contains("إنشاء")',
            'button:contains("Create")',
            'button:contains("جديد")',
            'button:contains("New")',
            'button:contains("إضافة")',
            'button:contains("Add")',
            '.btn:contains("إنشاء")',
            '.btn:contains("Create")',
            '[class*="create"]',
            '[id*="create"]',
            '[class*="new"]',
            '[id*="new"]'
        ];

        createSelectors.forEach(selector => {
            try {
                const buttons = this.findElementsByText(selector);
                buttons.forEach(button => {
                    this.markAsCreateButton(button);
                });
            } catch (e) {
                // تجاهل الأخطاء في المحددات
            }
        });
    }

    findElementsByText(selector) {
        const elements = [];

        // البحث في الأزرار
        const buttons = document.querySelectorAll('button, .btn, [role="button"]');
        buttons.forEach(btn => {
            const text = btn.textContent?.trim().toLowerCase();
            if (text && (
                text.includes('إنشاء') ||
                text.includes('create') ||
                text.includes('جديد') ||
                text.includes('new') ||
                text.includes('إضافة') ||
                text.includes('add')
            )) {
                elements.push(btn);
            }
        });

        return elements;
    }

    markAsCreateButton(button) {
        button.setAttribute('data-smart-form', 'create-button');
        button.style.border = '2px solid #28a745';

        button.addEventListener('click', (e) => {
            if (this.autoDetectionMode) {
                this.handleCreateButtonClick(button);
            }
            // لا نوقف التسجيل هنا - نتركه يستمر
            // if (this.isRecording) - تم إزالة هذا الشرط
        });
    }

    isCreateButton(element) {
        return element.getAttribute('data-smart-form') === 'create-button' ||
               this.findElementsByText('').includes(element);
    }

    handleCreateButtonClick(button) {
        console.log('تم النقر على زر الإنشاء:', button);
        this.waitForModal();
    }

    // ===== نظام التعرف على النوافذ المنبثقة =====

    setupModalObserver() {
        this.modalObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.checkForModal(node);
                    }
                });
            });
        });

        this.modalObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    checkForModal(element) {
        const modalSelectors = [
            '.modal',
            '.popup',
            '.dialog',
            '[role="dialog"]',
            '.overlay',
            '.modal-dialog',
            '.modal-content',
            '.popup-content'
        ];

        if (modalSelectors.some(selector => element.matches?.(selector))) {
            this.handleModalAppearance(element);
        }

        // البحث في العناصر الفرعية
        modalSelectors.forEach(selector => {
            const modals = element.querySelectorAll?.(selector);
            modals?.forEach(modal => this.handleModalAppearance(modal));
        });
    }

    handleModalAppearance(modal) {
        console.log('تم اكتشاف نافذة منبثقة:', modal);

        if (this.autoDetectionMode || this.isRecording) {
            this.analyzeModalContent(modal);
        }

        // إضافة تمييز للنافذة المنبثقة
        modal.style.outline = '3px solid #ffa500';
        modal.style.outlineOffset = '5px';

        // إذا كان التسجيل نشطاً، فعل التعلم الذكي في النافذة المنبثقة
        if (this.isRecording) {
            this.enableSmartLearningInModal(modal);
        }
    }

    analyzeModalContent(modal) {
        const formFields = modal.querySelectorAll('input, select, textarea');
        const dropdowns = modal.querySelectorAll('select, .dropdown');
        const radioButtons = modal.querySelectorAll('input[type="radio"]');
        const submitButtons = modal.querySelectorAll('button[type="submit"], .btn-primary, .btn-success, .submit-button');

        console.log(`تحليل النافذة المنبثقة:
            - ${formFields.length} حقل
            - ${dropdowns.length} قائمة منسدلة
            - ${radioButtons.length} زر راديو
            - ${submitButtons.length} زر إرسال`);

        // تحليل القوائم المنسدلة
        dropdowns.forEach(dropdown => {
            this.analyzeDropdown(dropdown);
        });

        // تحليل أزرار الراديو
        radioButtons.forEach(radio => {
            this.analyzeRadioButton(radio);
        });

        // تمييز أزرار الإرسال
        submitButtons.forEach(btn => {
            btn.style.outline = '3px solid #dc3545';
            btn.setAttribute('data-smart-form', 'submit-button');
            this.addElementLabel(btn, '✅ إرسال', '#dc3545');
        });

        // إذا كان التسجيل نشطاً، فعل التعلم الذكي
        if (this.isRecording) {
            setTimeout(() => {
                this.enableSmartLearningInModal(modal);
            }, 100);
        }
    }

    waitForModal() {
        return new Promise((resolve) => {
            const checkForModal = () => {
                const modal = document.querySelector('.modal, .popup, .dialog, [role="dialog"]');
                if (modal && modal.offsetParent !== null) {
                    this.handleModalAppearance(modal);
                    resolve(modal);
                } else {
                    setTimeout(checkForModal, 100);
                }
            };
            checkForModal();
        });
    }

    // ===== نظام التعرف على القوائم المنسدلة =====

    setupDropdownDetection() {
        const dropdowns = document.querySelectorAll('select, .dropdown');
        dropdowns.forEach(dropdown => {
            this.analyzeDropdown(dropdown);
        });
    }

    analyzeDropdown(dropdown) {
        if (dropdown.tagName === 'SELECT') {
            this.analyzeSelectDropdown(dropdown);
        } else {
            this.analyzeCustomDropdown(dropdown);
        }
    }

    analyzeSelectDropdown(select) {
        const options = Array.from(select.options).map(option => ({
            value: option.value,
            text: option.text.trim(),
            selected: option.selected
        }));

        this.dropdownOptions.set(this.generateSelector(select), {
            type: 'select',
            options: options,
            element: select
        });

        // إضافة تمييز
        select.style.outline = '2px solid #17a2b8';
        select.setAttribute('data-smart-dropdown', 'analyzed');

        console.log('تحليل قائمة منسدلة:', {
            selector: this.generateSelector(select),
            optionsCount: options.length,
            options: options
        });
    }

    analyzeCustomDropdown(dropdown) {
        // البحث عن خيارات القائمة المخصصة
        const optionSelectors = [
            '.dropdown-item',
            '.option',
            '.choice',
            'li',
            '[role="option"]'
        ];

        let options = [];
        optionSelectors.forEach(selector => {
            const items = dropdown.querySelectorAll(selector);
            items.forEach(item => {
                options.push({
                    text: item.textContent?.trim(),
                    element: item
                });
            });
        });

        if (options.length > 0) {
            this.dropdownOptions.set(this.generateSelector(dropdown), {
                type: 'custom',
                options: options,
                element: dropdown
            });

            dropdown.style.outline = '2px solid #17a2b8';
            dropdown.setAttribute('data-smart-dropdown', 'analyzed');

            console.log('تحليل قائمة منسدلة مخصصة:', {
                selector: this.generateSelector(dropdown),
                optionsCount: options.length,
                options: options
            });
        }
    }

    async handleDropdownSelection(element, step, data) {
        const dropdownInfo = this.dropdownOptions.get(step.selector);
        if (!dropdownInfo) return;

        if (data && data[this.currentDataRow]) {
            const targetValue = this.getValueForField(step, data[this.currentDataRow]);

            if (dropdownInfo.type === 'select') {
                const option = dropdownInfo.options.find(opt =>
                    opt.text.includes(targetValue) ||
                    opt.value === targetValue
                );

                if (option) {
                    element.value = option.value;
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                }
            } else if (dropdownInfo.type === 'custom') {
                const option = dropdownInfo.options.find(opt =>
                    opt.text.includes(targetValue)
                );

                if (option && option.element) {
                    option.element.click();
                }
            }
        }
    }

    // ===== نظام التعرف على أزرار الراديو =====

    detectRadioButtons() {
        const radioGroups = new Map();
        const radios = document.querySelectorAll('input[type="radio"]');

        radios.forEach(radio => {
            const groupName = radio.name || 'unnamed';
            if (!radioGroups.has(groupName)) {
                radioGroups.set(groupName, []);
            }
            radioGroups.get(groupName).push(radio);
        });

        radioGroups.forEach((radios, groupName) => {
            this.analyzeRadioGroup(groupName, radios);
        });
    }

    analyzeRadioGroup(groupName, radios) {
        const options = radios.map(radio => ({
            value: radio.value,
            text: this.getRadioLabel(radio),
            element: radio
        }));

        console.log(`مجموعة أزرار راديو "${groupName}":`, options);

        radios.forEach(radio => {
            radio.parentElement.style.outline = '2px solid #6f42c1';
            radio.setAttribute('data-smart-radio-group', groupName);
        });
    }

    analyzeRadioButton(radio) {
        const label = this.getRadioLabel(radio);
        const groupName = radio.name || 'unnamed';

        console.log('تحليل زر راديو:', {
            name: groupName,
            value: radio.value,
            label: label
        });
    }

    getRadioLabel(radio) {
        // البحث عن التسمية المرتبطة
        if (radio.id) {
            const label = document.querySelector(`label[for="${radio.id}"]`);
            if (label) return label.textContent?.trim();
        }

        // البحث في العنصر الأب
        const parent = radio.parentElement;
        if (parent) {
            const text = parent.textContent?.replace(radio.value || '', '').trim();
            if (text) return text;
        }

        // البحث في العنصر التالي
        const nextSibling = radio.nextElementSibling;
        if (nextSibling && nextSibling.textContent) {
            return nextSibling.textContent.trim();
        }

        return radio.value || 'غير محدد';
    }

    getValueForField(step, rowData) {
        // محاولة العثور على القيمة المناسبة من بيانات الصف
        const fieldName = step.placeholder || step.selector;

        // البحث المباشر
        if (rowData[fieldName]) {
            return rowData[fieldName];
        }

        // البحث بالكلمات المفتاحية
        const keywords = Object.keys(rowData);
        for (const key of keywords) {
            if (fieldName.toLowerCase().includes(key.toLowerCase()) ||
                key.toLowerCase().includes(fieldName.toLowerCase())) {
                return rowData[key];
            }
        }

        return '';
    }

    // ===== التعامل مع النوافذ المنبثقة أثناء التسجيل =====

    handleModalForRecording() {
        // البحث عن النافذة المنبثقة الجديدة
        const modalSelectors = [
            '.modal:not([data-smart-processed])',
            '.popup:not([data-smart-processed])',
            '.dialog:not([data-smart-processed])',
            '[role="dialog"]:not([data-smart-processed])'
        ];

        let foundModal = null;
        for (const selector of modalSelectors) {
            const modals = document.querySelectorAll(selector);
            for (const modal of modals) {
                if (modal.offsetParent !== null) { // مرئية
                    foundModal = modal;
                    break;
                }
            }
            if (foundModal) break;
        }

        if (foundModal) {
            this.enableSmartLearningInModal(foundModal);
        } else {
            // إعادة المحاولة بعد وقت قصير
            setTimeout(() => {
                this.handleModalForRecording();
            }, 200);
        }
    }

    enableSmartLearningInModal(modal) {
        console.log('تفعيل التعلم الذكي في النافذة المنبثقة:', modal);

        // تمييز النافذة كمعالجة
        modal.setAttribute('data-smart-processed', 'true');

        // إضافة تمييز بصري للنافذة
        modal.style.boxShadow = '0 0 20px #4facfe';

        // تحليل وتمييز جميع الحقول في النافذة
        this.highlightModalFields(modal);

        // إضافة مستمعي الأحداث للحقول
        this.addModalFieldListeners(modal);

        // إظهار رسالة للمستخدم
        this.showModalLearningNotification(modal);
    }

    highlightModalFields(modal) {
        // العثور على جميع الحقول في النافذة المنبثقة
        const fields = modal.querySelectorAll(`
            input[type="text"],
            input[type="email"],
            input[type="number"],
            input[type="tel"],
            input[type="password"],
            textarea,
            select,
            input[type="radio"],
            input[type="checkbox"]
        `);

        fields.forEach((field, index) => {
            // إضافة تمييز للحقل
            field.style.outline = '2px solid #4facfe';
            field.style.outlineOffset = '2px';
            field.setAttribute('data-smart-field', 'true');

            // إضافة تسمية للحقل
            this.addFieldLabel(field, index);

            // إضافة مستمع للنقر الأيمن
            field.addEventListener('contextmenu', (e) => {
                if (this.isRecording) {
                    e.preventDefault();
                    this.showFieldMappingMenu(e, field);
                }
            });
        });

        console.log(`تم تمييز ${fields.length} حقل في النافذة المنبثقة`);
    }

    addFieldLabel(field, index) {
        const label = document.createElement('div');
        label.className = 'smart-field-label';
        label.style.cssText = `
            position: absolute;
            top: -25px;
            right: -5px;
            background: #4facfe;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10000;
            pointer-events: none;
            white-space: nowrap;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        `;

        // تحديد نوع الحقل
        const fieldType = this.getFieldType(field);
        label.textContent = `${fieldType} ${index + 1}`;

        // تأكد من أن العنصر له position relative
        const computedStyle = window.getComputedStyle(field);
        if (computedStyle.position === 'static') {
            field.style.position = 'relative';
        }

        field.appendChild(label);
    }

    getFieldType(field) {
        const tagName = field.tagName.toLowerCase();
        const type = field.type?.toLowerCase();

        if (tagName === 'select') return '📋 قائمة';
        if (type === 'radio') return '🔘 راديو';
        if (type === 'checkbox') return '☑️ خانة';
        if (tagName === 'textarea') return '📝 نص طويل';
        if (type === 'email') return '📧 إيميل';
        if (type === 'number') return '🔢 رقم';
        if (type === 'tel') return '📞 هاتف';
        return '📝 نص';
    }

    addModalFieldListeners(modal) {
        // إضافة مستمعي الأحداث للحقول
        const fields = modal.querySelectorAll('[data-smart-field]');

        fields.forEach(field => {
            // مستمع للتركيز
            field.addEventListener('focus', (e) => {
                if (this.isRecording) {
                    e.target.style.boxShadow = '0 0 10px #4facfe';
                }
            });

            // مستمع لفقدان التركيز
            field.addEventListener('blur', (e) => {
                if (this.isRecording) {
                    e.target.style.boxShadow = '';
                }
            });
        });
    }

    showModalLearningNotification(modal) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10001;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
            animation: slideInRight 0.5s ease-out;
            direction: rtl;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>🤖</span>
                <span>التعلم الذكي مفعل</span>
            </div>
            <div style="font-size: 10px; opacity: 0.9; margin-top: 2px;">
                انقر بالزر الأيمن على الحقول لربطها
            </div>
        `;

        // إضافة الأنيميشن
        if (!document.getElementById('modal-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'modal-notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        modal.appendChild(notification);

        // إزالة الإشعار بعد 5 ثوان
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    showFieldMappingMenu(event, field) {
        // إظهار قائمة ربط الحقول (مشابهة لوضع التعليم العادي)
        this.selectedElement = field;
        this.showContextMenu(event.clientX, event.clientY, false);
    }

    // ===== مؤشرات التسجيل البصرية =====

    showRecordingIndicator() {
        // إزالة المؤشر السابق إن وجد
        this.hideRecordingIndicator();

        const indicator = document.createElement('div');
        indicator.id = 'smart-recording-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            display: flex;
            align-items: center;
            gap: 10px;
            animation: recordingPulse 1.5s infinite;
            pointer-events: none;
            direction: rtl;
        `;

        indicator.innerHTML = `
            <div style="
                width: 12px;
                height: 12px;
                background: white;
                border-radius: 50%;
                animation: recordingDot 1s infinite;
            "></div>
            <span>🔴 جاري التسجيل...</span>
            <span id="steps-counter" style="
                background: rgba(255,255,255,0.2);
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 12px;
            ">0 خطوة</span>
        `;

        // إضافة الأنيميشن
        if (!document.getElementById('recording-indicator-styles')) {
            const style = document.createElement('style');
            style.id = 'recording-indicator-styles';
            style.textContent = `
                @keyframes recordingPulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                }
                @keyframes recordingDot {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.3; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(indicator);

        // تحديث العداد
        this.updateRecordingCounter();
    }

    hideRecordingIndicator() {
        const indicator = document.getElementById('smart-recording-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    updateRecordingCounter() {
        const counter = document.getElementById('steps-counter');
        if (counter) {
            counter.textContent = `${this.recordedSteps.length} خطوة`;

            // تأثير بصري عند التحديث
            counter.style.background = 'rgba(255,255,255,0.4)';
            counter.style.transform = 'scale(1.1)';

            setTimeout(() => {
                counter.style.background = 'rgba(255,255,255,0.2)';
                counter.style.transform = 'scale(1)';
            }, 200);
        }
    }

    // تحديث العداد عند تسجيل خطوة جديدة
    recordStep(step) {
        this.recordedSteps.push(step);
        this.updateRecordingCounter();

        // إرسال تحديث للـ popup
        chrome.runtime.sendMessage({
            action: 'stepRecorded',
            step: step,
            totalSteps: this.recordedSteps.length
        });
    }

    // ===== نظام التعبئة الذكية الجديد =====

    async startSmartAutoFill(data, rowIndex, selectedFields) {
        try {
            SmartFormUtils.showNotification('بدء التعبئة الذكية...', 'info');

            // تحميل الإعدادات المحفوظة
            await this.loadSmartSettings();

            // البحث عن الحقول المطابقة تلقائياً
            const detectedFields = this.detectMatchingFields(data);

            // تعبئة الحقول المكتشفة
            await this.fillDetectedFields(detectedFields, data);

            // إرسال تحديث التقدم
            this.updateProgress(100);

            SmartFormUtils.showNotification('تم الانتهاء من التعبئة الذكية', 'success');

        } catch (error) {
            console.error('خطأ في التعبئة الذكية:', error);
            SmartFormUtils.showNotification('خطأ في التعبئة الذكية', 'error');
        }
    }

    async loadSmartSettings() {
        try {
            const result = await chrome.storage.local.get(['smartFieldMappings', 'autoFillSettings']);
            this.smartFieldMappings = result.smartFieldMappings || {};
            this.autoFillSettings = result.autoFillSettings || {};
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات الذكية:', error);
        }
    }

    detectMatchingFields(data) {
        const detectedFields = {};
        const formFields = SmartFormUtils.getFormFields();

        formFields.forEach(field => {
            const fieldInfo = this.analyzeField(field.element);
            const matchedColumn = this.findMatchingColumn(fieldInfo, data);

            if (matchedColumn) {
                detectedFields[SmartFormUtils.getElementSelector(field.element)] = {
                    element: field.element,
                    column: matchedColumn,
                    value: data[matchedColumn],
                    confidence: this.calculateConfidence(fieldInfo, matchedColumn)
                };
            }
        });

        console.log('الحقول المكتشفة:', detectedFields);
        return detectedFields;
    }

    analyzeField(element) {
        const info = {
            tagName: element.tagName.toLowerCase(),
            type: element.type || '',
            name: element.name || '',
            id: element.id || '',
            placeholder: element.placeholder || '',
            className: element.className || '',
            label: this.getFieldLabel(element)
        };

        return info;
    }

    getFieldLabel(element) {
        // البحث عن التسمية المرتبطة
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) return label.textContent?.trim();
        }

        // البحث في العنصر الأب
        const parent = element.parentElement;
        if (parent) {
            const label = parent.querySelector('label');
            if (label) return label.textContent?.trim();
        }

        // البحث في العنصر السابق
        const prevSibling = element.previousElementSibling;
        if (prevSibling && prevSibling.tagName === 'LABEL') {
            return prevSibling.textContent?.trim();
        }

        return '';
    }

    findMatchingColumn(fieldInfo, data) {
        const dataColumns = Object.keys(data);
        let bestMatch = null;
        let bestScore = 0;

        dataColumns.forEach(column => {
            const score = this.calculateMatchScore(fieldInfo, column);
            if (score > bestScore && score > 0.5) { // حد أدنى للثقة
                bestScore = score;
                bestMatch = column;
            }
        });

        return bestMatch;
    }

    calculateMatchScore(fieldInfo, column) {
        let score = 0;
        const columnLower = column.toLowerCase();

        // مطابقة الاسم
        if (fieldInfo.name && fieldInfo.name.toLowerCase().includes(columnLower)) {
            score += 0.4;
        }

        // مطابقة المعرف
        if (fieldInfo.id && fieldInfo.id.toLowerCase().includes(columnLower)) {
            score += 0.3;
        }

        // مطابقة النص التوضيحي
        if (fieldInfo.placeholder && fieldInfo.placeholder.toLowerCase().includes(columnLower)) {
            score += 0.2;
        }

        // مطابقة التسمية
        if (fieldInfo.label && fieldInfo.label.toLowerCase().includes(columnLower)) {
            score += 0.3;
        }

        // مطابقات خاصة
        const specialMatches = {
            'name': ['اسم', 'name', 'title', 'عنوان'],
            'sku': ['sku', 'code', 'رمز', 'كود'],
            'price': ['price', 'سعر', 'cost', 'تكلفة'],
            'category': ['category', 'فئة', 'نوع', 'type'],
            'unit': ['unit', 'وحدة', 'measure', 'قياس']
        };

        Object.entries(specialMatches).forEach(([key, keywords]) => {
            if (columnLower.includes(key)) {
                keywords.forEach(keyword => {
                    if (fieldInfo.name?.toLowerCase().includes(keyword) ||
                        fieldInfo.id?.toLowerCase().includes(keyword) ||
                        fieldInfo.placeholder?.toLowerCase().includes(keyword) ||
                        fieldInfo.label?.toLowerCase().includes(keyword)) {
                        score += 0.5;
                    }
                });
            }
        });

        return Math.min(score, 1); // الحد الأقصى 1
    }

    calculateConfidence(fieldInfo, column) {
        return this.calculateMatchScore(fieldInfo, column);
    }

    updateProgress(percentage) {
        // إرسال تحديث التقدم لعارض البيانات
        chrome.runtime.sendMessage({
            action: 'updateProgress',
            percentage: percentage
        });
    }

    // ===== النظام الذكي الجديد =====

    enableSmartSystem(dataViewerTabId) {
        this.dataViewerTabId = dataViewerTabId;
        this.smartSystemEnabled = true;

        // تفعيل التعرف التلقائي
        this.autoDetectionMode = true;
        this.analyzePageElements();

        // إظهار إشعار التفعيل
        SmartFormUtils.showNotification('تم تفعيل النظام الذكي - انقر بالزر الأيمن على الحقول لربطها', 'success');

        console.log('تم تفعيل النظام الذكي');
    }

    enableSmartLearningSystem(dataViewerTabId, currentData) {
        this.dataViewerTabId = dataViewerTabId;
        this.smartSystemEnabled = true;
        this.learningMode = true;
        this.currentData = currentData;

        // تفعيل التعرف التلقائي
        this.autoDetectionMode = true;
        this.analyzePageElements();

        // إعداد نظام النقر الأيمن للتعلم
        this.setupLearningRightClickSystem();

        // طلب بيانات الصف الحالي
        this.requestCurrentRowData();

        // إظهار إشعار التفعيل
        SmartFormUtils.showNotification('🤖 وضع التعلم الذكي مفعل - انقر بالزر الأيمن لربط الحقول', 'success');

        console.log('تم تفعيل وضع التعلم الذكي');
    }

    setupLearningRightClickSystem() {
        // إزالة المستمعات السابقة
        document.removeEventListener('contextmenu', this.handleLearningRightClick);

        // إضافة مستمع النقر الأيمن للتعلم
        this.handleLearningRightClick = this.handleLearningRightClick.bind(this);
        document.addEventListener('contextmenu', this.handleLearningRightClick, true);

        console.log('تم إعداد نظام النقر الأيمن للتعلم');
    }

    handleLearningRightClick(event) {
        if (!this.learningMode) return;

        const element = event.target;

        // التحقق من أن العنصر حقل إدخال
        if (this.isFormField(element)) {
            event.preventDefault();
            event.stopPropagation();

            // تحليل معلومات الحقل
            const fieldInfo = this.analyzeFieldForMapping(element);

            // إرسال طلب اختيار العمود لعارض البيانات
            chrome.runtime.sendMessage({
                action: 'requestColumnSelection',
                fieldInfo: fieldInfo
            });

            // تمييز الحقل المحدد
            this.highlightSelectedField(element);

            // حفظ مرجع للحقل الحالي
            this.currentSelectedField = element;
        }
    }

    requestCurrentRowData() {
        // طلب بيانات الصف الحالي من عارض البيانات
        chrome.runtime.sendMessage({
            action: 'requestCurrentRowData'
        });
    }

    receiveCurrentRowData(rowData, rowIndex, columns, isNewRow = false) {
        this.currentRowData = rowData;
        this.currentRowIndex = rowIndex;
        this.availableColumns = columns;

        if (isNewRow) {
            // صف جديد - تعبئة تلقائية للحقول المربوطة
            this.autoFillMappedFields();
            SmartFormUtils.showNotification(`🔄 تعبئة تلقائية للصف ${rowIndex + 1}`, 'info');
        } else {
            // نفس الصف - تحديث البيانات فقط
            SmartFormUtils.showNotification(`📊 تم تحميل بيانات الصف ${rowIndex + 1}`, 'info');
        }

        console.log('تم استلام بيانات الصف:', rowData);
    }

    async autoFillMappedFields() {
        try {
            // تحميل المطابقات المحفوظة
            const result = await chrome.storage.local.get(['fieldMappings']);
            const fieldMappings = result.fieldMappings || {};

            let filledCount = 0;

            // تعبئة الحقول المربوطة
            for (const [selector, mapping] of Object.entries(fieldMappings)) {
                const element = document.querySelector(selector);
                if (element && this.currentRowData && this.currentRowData[mapping.column] !== undefined) {
                    await this.fillSmartField(element, this.currentRowData[mapping.column]);
                    filledCount++;

                    // تمييز الحقل المعبأ
                    this.highlightFilledField(element, mapping.column);

                    // انتظار قصير بين الحقول
                    await this.delay(200);
                }
            }

            if (filledCount > 0) {
                SmartFormUtils.showNotification(`✅ تم تعبئة ${filledCount} حقل تلقائياً`, 'success');

                // إرسال إشعار الإكمال
                setTimeout(() => {
                    this.requestMoveToNextRow();
                }, 2000);
            }

        } catch (error) {
            console.error('خطأ في التعبئة التلقائية:', error);
        }
    }

    highlightFilledField(element, columnName) {
        // تمييز الحقل المعبأ
        element.style.outline = '2px solid #28a745';
        element.style.outlineOffset = '2px';
        element.style.background = 'rgba(212, 237, 218, 0.3)';

        // إضافة تسمية
        const label = document.createElement('div');
        label.className = 'auto-filled-label';
        label.style.cssText = `
            position: absolute;
            top: -25px;
            right: 0;
            background: #28a745;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            z-index: 10000;
            pointer-events: none;
            white-space: nowrap;
        `;
        label.textContent = `✅ ${columnName}`;

        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(label);

        // إزالة التمييز بعد 3 ثوان
        setTimeout(() => {
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.style.background = '';
            if (label.parentNode) {
                label.remove();
            }
        }, 3000);
    }

    requestMoveToNextRow() {
        // طلب الانتقال للصف التالي
        chrome.runtime.sendMessage({
            action: 'moveToNextRow'
        });
    }

    setupRightClickSystem(dataViewerTabId) {
        this.dataViewerTabId = dataViewerTabId;

        // إزالة المستمعات السابقة
        document.removeEventListener('contextmenu', this.handleRightClick);

        // إضافة مستمع النقر الأيمن
        this.handleRightClick = this.handleRightClick.bind(this);
        document.addEventListener('contextmenu', this.handleRightClick, true);

        console.log('تم إعداد نظام النقر الأيمن');
    }

    handleRightClick(event) {
        const element = event.target;

        // التحقق من أن العنصر حقل إدخال
        if (this.isFormField(element)) {
            event.preventDefault();
            event.stopPropagation();

            // تحليل معلومات الحقل
            const fieldInfo = this.analyzeFieldForMapping(element);

            // إرسال طلب اختيار العمود لعارض البيانات
            chrome.runtime.sendMessage({
                action: 'requestColumnSelection',
                fieldInfo: fieldInfo
            });

            // تمييز الحقل المحدد
            this.highlightSelectedField(element);
        }
    }

    isFormField(element) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type?.toLowerCase();

        return (
            tagName === 'input' && ['text', 'email', 'number', 'tel', 'password', 'radio', 'checkbox'].includes(type) ||
            tagName === 'textarea' ||
            tagName === 'select'
        );
    }

    analyzeFieldForMapping(element) {
        return {
            selector: this.generateSelector(element),
            tagName: element.tagName.toLowerCase(),
            type: element.type || '',
            name: element.name || '',
            id: element.id || '',
            placeholder: element.placeholder || '',
            className: element.className || '',
            label: this.getFieldLabel(element),
            value: element.value || ''
        };
    }

    highlightSelectedField(element) {
        // إزالة التمييز السابق
        document.querySelectorAll('.smart-field-selected').forEach(el => {
            el.classList.remove('smart-field-selected');
            el.style.outline = '';
            el.style.outlineOffset = '';
        });

        // تمييز الحقل الحالي
        element.classList.add('smart-field-selected');
        element.style.outline = '3px solid #ff6b6b';
        element.style.outlineOffset = '2px';

        // إضافة تسمية مؤقتة
        const label = document.createElement('div');
        label.className = 'smart-field-temp-label';
        label.style.cssText = `
            position: absolute;
            top: -30px;
            right: 0;
            background: #ff6b6b;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10000;
            pointer-events: none;
            white-space: nowrap;
        `;
        label.textContent = '🎯 جاري الربط...';

        // تأكد من أن العنصر له position relative
        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(label);

        // إزالة التسمية بعد 3 ثوان
        setTimeout(() => {
            if (label.parentNode) {
                label.remove();
            }
        }, 3000);
    }

    applyFieldMapping(mapping, value) {
        try {
            const element = document.querySelector(mapping.fieldSelector);
            if (!element) {
                console.error('لم يتم العثور على الحقل:', mapping.fieldSelector);
                return;
            }

            // تطبيق القيمة على الحقل
            this.fillSmartField(element, value);

            // تمييز الحقل المربوط
            element.style.outline = '3px solid #28a745';
            element.style.outlineOffset = '2px';

            // إضافة تسمية النجاح
            const successLabel = document.createElement('div');
            successLabel.style.cssText = `
                position: absolute;
                top: -30px;
                right: 0;
                background: #28a745;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 10000;
                pointer-events: none;
                white-space: nowrap;
            `;
            successLabel.textContent = `✅ مربوط: ${mapping.column}`;

            const computedStyle = window.getComputedStyle(element);
            if (computedStyle.position === 'static') {
                element.style.position = 'relative';
            }

            element.appendChild(successLabel);

            // إزالة التمييز والتسمية بعد 2 ثانية
            setTimeout(() => {
                element.style.outline = '';
                element.style.outlineOffset = '';
                if (successLabel.parentNode) {
                    successLabel.remove();
                }
            }, 2000);

            // حفظ المطابقة محلياً
            this.saveFieldMapping(mapping);

            SmartFormUtils.showNotification(`تم ربط الحقل بالعمود: ${mapping.column}`, 'success');

        } catch (error) {
            console.error('خطأ في تطبيق المطابقة:', error);
            SmartFormUtils.showNotification('خطأ في تطبيق المطابقة', 'error');
        }
    }

    applyFieldMappingWithData(mapping, value, rowData, rowIndex) {
        try {
            const element = document.querySelector(mapping.fieldSelector);
            if (!element) {
                console.error('لم يتم العثور على الحقل:', mapping.fieldSelector);
                return;
            }

            // حفظ بيانات الصف الحالي
            this.currentRowData = rowData;
            this.currentRowIndex = rowIndex;

            // تطبيق القيمة على الحقل
            this.fillSmartField(element, value);

            // تمييز الحقل المربوط
            this.highlightMappedField(element, mapping.column);

            // حفظ المطابقة محلياً
            this.saveFieldMapping(mapping);

            // إرسال إشعار إكمال الربط
            chrome.runtime.sendMessage({
                action: 'fieldMappingCompleted',
                mapping: mapping
            });

            SmartFormUtils.showNotification(`✅ تم ربط وتعبئة: ${mapping.column}`, 'success');

        } catch (error) {
            console.error('خطأ في تطبيق المطابقة مع البيانات:', error);
            SmartFormUtils.showNotification('خطأ في تطبيق المطابقة', 'error');
        }
    }

    highlightMappedField(element, columnName) {
        // إزالة التمييز السابق
        element.classList.remove('smart-field-selected');

        // تمييز الحقل المربوط
        element.style.outline = '3px solid #28a745';
        element.style.outlineOffset = '2px';
        element.style.background = 'rgba(212, 237, 218, 0.5)';

        // إضافة تسمية النجاح
        const successLabel = document.createElement('div');
        successLabel.className = 'mapping-success-label';
        successLabel.style.cssText = `
            position: absolute;
            top: -30px;
            right: 0;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: bold;
            z-index: 10000;
            pointer-events: none;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        `;
        successLabel.innerHTML = `✅ مربوط: <strong>${columnName}</strong>`;

        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(successLabel);

        // تأثير نابض
        element.style.animation = 'mappingSuccess 0.6s ease-out';

        // إضافة الأنيميشن إذا لم يكن موجوداً
        if (!document.getElementById('mapping-success-animation')) {
            const style = document.createElement('style');
            style.id = 'mapping-success-animation';
            style.textContent = `
                @keyframes mappingSuccess {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            `;
            document.head.appendChild(style);
        }

        // إزالة التمييز بعد 3 ثوان
        setTimeout(() => {
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.style.background = '';
            element.style.animation = '';
            if (successLabel.parentNode) {
                successLabel.remove();
            }
        }, 3000);
    }

    handleNoDataAvailable() {
        SmartFormUtils.showNotification('⚠️ لا توجد بيانات متاحة للتعبئة', 'warning');
    }

    disableLearningMode() {
        this.learningMode = false;

        // إزالة مستمعي الأحداث
        document.removeEventListener('contextmenu', this.handleLearningRightClick);

        // إزالة جميع التمييزات
        document.querySelectorAll('.smart-field-selected, .mapping-success-label, .auto-filled-label').forEach(el => {
            if (el.classList) {
                el.classList.remove('smart-field-selected');
            } else {
                el.remove();
            }
        });

        SmartFormUtils.showNotification('تم إيقاف وضع التعلم', 'info');
    }

    async saveFieldMapping(mapping) {
        try {
            const result = await chrome.storage.local.get(['fieldMappings']);
            const fieldMappings = result.fieldMappings || {};
            fieldMappings[mapping.fieldSelector] = mapping;
            await chrome.storage.local.set({ fieldMappings });
        } catch (error) {
            console.error('خطأ في حفظ المطابقة:', error);
        }
    }

    async fillNextRow(rowData, rowIndex) {
        try {
            // تحميل المطابقات المحفوظة
            const result = await chrome.storage.local.get(['fieldMappings']);
            const fieldMappings = result.fieldMappings || {};

            let filledCount = 0;
            const totalMappings = Object.keys(fieldMappings).length;

            // تعبئة الحقول المربوطة
            for (const [selector, mapping] of Object.entries(fieldMappings)) {
                const element = document.querySelector(selector);
                if (element && rowData[mapping.column] !== undefined) {
                    await this.fillSmartField(element, rowData[mapping.column]);
                    filledCount++;

                    // انتظار قصير بين الحقول
                    await this.delay(300);
                }
            }

            // إشعار بإكمال الصف
            SmartFormUtils.showNotification(`تم تعبئة ${filledCount} حقل من الصف ${rowIndex + 1}`, 'success');

            // إرسال إشعار الإكمال لعارض البيانات
            chrome.runtime.sendMessage({
                action: 'rowCompleted',
                rowIndex: rowIndex,
                filledCount: filledCount
            });

        } catch (error) {
            console.error('خطأ في تعبئة الصف:', error);
            SmartFormUtils.showNotification('خطأ في تعبئة الصف', 'error');
        }
    }

    handleAllRowsCompleted() {
        SmartFormUtils.showNotification('🎉 تم إكمال جميع الصفوف المحددة!', 'success');

        // إزالة جميع التمييزات
        document.querySelectorAll('.smart-field-selected').forEach(el => {
            el.classList.remove('smart-field-selected');
            el.style.outline = '';
            el.style.outlineOffset = '';
        });
    }


}

// Initialize content script
const smartFormContent = new SmartFormContent();
