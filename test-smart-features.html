<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الميزات الذكية - Smart Form Filler</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            background: #fafafa;
        }
        
        .section h2 {
            color: #333;
            margin-top: 0;
            font-size: 1.8em;
            border-bottom: 3px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .create-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .create-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .create-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            direction: rtl;
        }
        
        .modal-header {
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 15px;
            margin-bottom: 25px;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #333;
            font-size: 1.5em;
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .radio-option input[type="radio"] {
            width: auto;
        }
        
        .dropdown-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }
        
        .custom-dropdown {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .dropdown-button {
            background: white;
            border: 2px solid #e0e0e0;
            padding: 12px;
            width: 100%;
            text-align: right;
            cursor: pointer;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: white;
            min-width: 100%;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            top: 100%;
            right: 0;
        }
        
        .dropdown-content.show {
            display: block;
        }
        
        .dropdown-item {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .dropdown-item:hover {
            background-color: #f1f1f1;
        }
        
        .submit-button {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .radio-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .instructions {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #333;
        }
        
        .instructions ol {
            margin: 0;
            padding-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 اختبار الميزات الذكية</h1>
            <p>اختبر التعرف التلقائي وتسجيل الخطوات</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h3>📋 تعليمات الاختبار</h3>
                <ol>
                    <li>افتح إضافة Smart Form Filler</li>
                    <li>فعل وضع "التعرف التلقائي" لرؤية العناصر المكتشفة</li>
                    <li>اضغط "بدء التسجيل" لتسجيل تسلسل العمليات</li>
                    <li>انقر على زر "إنشاء منتج جديد" لفتح النافذة المنبثقة</li>
                    <li>املأ النموذج واختر من القوائم المنسدلة</li>
                    <li>اضغط "إيقاف التسجيل" ثم "تشغيل التسجيل" لإعادة التشغيل</li>
                </ol>
            </div>
            
            <!-- قسم أزرار الإنشاء -->
            <div class="section create-section">
                <h2>🔘 أزرار الإنشاء</h2>
                <p>هذه الأزرار سيتم التعرف عليها تلقائياً كأزرار إنشاء:</p>
                <button class="create-button" onclick="openModal()">إنشاء منتج جديد</button>
                <button class="create-button" onclick="openModal()">Create New Product</button>
                <button class="create-button" onclick="openModal()">إضافة عنصر</button>
                <button class="create-button" onclick="openModal()">Add New Item</button>
            </div>
            
            <!-- قسم القوائم المنسدلة -->
            <div class="section dropdown-section">
                <h2>📋 القوائم المنسدلة</h2>
                <p>قوائم منسدلة مختلفة للاختبار:</p>
                
                <div class="form-group">
                    <label>قائمة منسدلة عادية:</label>
                    <select id="normal-dropdown">
                        <option value="">-- اختر خيار --</option>
                        <option value="option1">الخيار الأول</option>
                        <option value="option2">الخيار الثاني</option>
                        <option value="option3">الخيار الثالث</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>قائمة منسدلة مخصصة:</label>
                    <div class="custom-dropdown">
                        <div class="dropdown-button" onclick="toggleDropdown()">
                            <span id="selected-option">اختر خيار</span>
                            <span>▼</span>
                        </div>
                        <div class="dropdown-content" id="dropdown-content">
                            <div class="dropdown-item" onclick="selectOption('خيار مخصص 1')">خيار مخصص 1</div>
                            <div class="dropdown-item" onclick="selectOption('خيار مخصص 2')">خيار مخصص 2</div>
                            <div class="dropdown-item" onclick="selectOption('خيار مخصص 3')">خيار مخصص 3</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قسم أزرار الراديو -->
            <div class="section radio-section">
                <h2>🔘 أزرار الراديو</h2>
                <p>مجموعات مختلفة من أزرار الراديو:</p>
                
                <div class="form-group">
                    <label>نوع المنتج:</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="physical" name="product-type" value="physical">
                            <label for="physical">منتج مادي</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="digital" name="product-type" value="digital">
                            <label for="digital">منتج رقمي</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="service" name="product-type" value="service">
                            <label for="service">خدمة</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>حالة التوفر:</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="available" name="availability" value="available">
                            <label for="available">متوفر</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="out-of-stock" name="availability" value="out-of-stock">
                            <label for="out-of-stock">غير متوفر</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- النافذة المنبثقة -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="closeModal()">&times;</span>
                <h3>إنشاء منتج جديد</h3>
            </div>
            
            <form id="product-form">
                <div class="form-group">
                    <label for="product-name">اسم المنتج *</label>
                    <input type="text" id="product-name" name="name" placeholder="أدخل اسم المنتج" required>
                </div>
                
                <div class="form-group">
                    <label for="product-sku">رمز المنتج (SKU) *</label>
                    <input type="text" id="product-sku" name="sku" placeholder="أدخل رمز المنتج" required>
                </div>
                
                <div class="form-group">
                    <label for="product-price">السعر *</label>
                    <input type="number" id="product-price" name="price" placeholder="أدخل السعر" step="0.01" required>
                </div>
                
                <div class="form-group">
                    <label for="product-category">الفئة *</label>
                    <select id="product-category" name="category" required>
                        <option value="">-- اختر الفئة --</option>
                        <option value="electronics">إلكترونيات</option>
                        <option value="clothing">ملابس</option>
                        <option value="books">كتب</option>
                        <option value="home">منزل وحديقة</option>
                        <option value="sports">رياضة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="product-unit">الوحدة *</label>
                    <select id="product-unit" name="unit" required>
                        <option value="">-- اختر الوحدة --</option>
                        <option value="piece">قطعة</option>
                        <option value="kg">كيلوجرام</option>
                        <option value="liter">لتر</option>
                        <option value="meter">متر</option>
                        <option value="box">صندوق</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>نوع المنتج:</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="modal-physical" name="modal-type" value="physical" checked>
                            <label for="modal-physical">مادي</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="modal-digital" name="modal-type" value="digital">
                            <label for="modal-digital">رقمي</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="product-description">الوصف</label>
                    <textarea id="product-description" name="description" rows="3" placeholder="أدخل وصف المنتج"></textarea>
                </div>
                
                <button type="submit" class="submit-button">حفظ المنتج</button>
            </form>
        </div>
    </div>
    
    <script>
        function openModal() {
            document.getElementById('product-modal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('product-modal').style.display = 'none';
        }
        
        function toggleDropdown() {
            document.getElementById('dropdown-content').classList.toggle('show');
        }
        
        function selectOption(option) {
            document.getElementById('selected-option').textContent = option;
            document.getElementById('dropdown-content').classList.remove('show');
        }
        
        // إغلاق القائمة المنسدلة عند النقر خارجها
        window.onclick = function(event) {
            if (!event.target.matches('.dropdown-button')) {
                var dropdowns = document.getElementsByClassName('dropdown-content');
                for (var i = 0; i < dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains('show')) {
                        openDropdown.classList.remove('show');
                    }
                }
            }
            
            // إغلاق النافذة المنبثقة عند النقر خارجها
            if (event.target == document.getElementById('product-modal')) {
                closeModal();
            }
        }
        
        // معالج إرسال النموذج
        document.getElementById('product-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم حفظ المنتج بنجاح! 🎉');
            closeModal();
        });
    </script>
</body>
</html>
