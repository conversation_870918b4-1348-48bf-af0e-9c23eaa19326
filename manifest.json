{"manifest_version": 3, "name": "Smart Form Filler", "version": "1.0.0", "description": "إضافة ذكية لتعبئة النماذج تلقائياً باستخدام بيانات مخزنة مسبقاً", "permissions": ["storage", "activeTab", "contextMenus", "tabs", "scripting"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["utils.js", "content.js", "field-selector.js"], "css": ["content-styles.css"], "run_at": "document_end"}], "action": {"default_popup": "popup.html", "default_title": "Smart Form Filler", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["popup.html", "styles.css", "data-viewer.html", "data-viewer.js", "split-view.html", "split-view.js", "test-form.html", "test-teaching-mode.html", "test-smart-features.html"], "matches": ["<all_urls>"]}]}