// ===== Field Selector Content Script =====
// يعمل داخل الصفحات المستهدفة لتحديد الحقول وربطها

class FieldSelector {
    constructor() {
        console.log('🚀 بدء تهيئة Field Selector...');

        this.isActive = false;
        this.selectedFields = {};
        this.columns = [];
        this.sampleData = {};
        this.overlay = null;
        this.highlightedElement = null;

        this.setupMessageListener();
        console.log('🎯 Field Selector مُحمل ومستعد - URL:', window.location.href);

        // إضافة مؤشر بصري للتأكد من التحميل
        this.addLoadIndicator();
    }

    addLoadIndicator() {
        // إضافة مؤشر صغير للتأكد من تحميل الـ script
        const indicator = document.createElement('div');
        indicator.id = 'field-selector-loaded';
        indicator.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 9998;
            opacity: 0.8;
            direction: rtl;
        `;
        indicator.textContent = '🎯 Field Selector جاهز';
        document.body.appendChild(indicator);

        // إزالة المؤشر بعد 3 ثوان
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 3000);
    }

    setupMessageListener() {
        console.log('📡 إعداد مستمع الرسائل...');

        window.addEventListener('message', (event) => {
            console.log('📨 رسالة واردة:', event.data);

            if (event.data && event.data.action) {
                console.log('✅ معالجة الرسالة:', event.data.action);
                this.handleMessage(event.data);
            }
        });

        // إضافة مستمع إضافي للرسائل من parent
        if (window.parent !== window) {
            console.log('🔗 إضافة مستمع للـ parent window');
        }
    }

    handleMessage(data) {
        console.log('🔄 معالجة الرسالة:', data.action);

        try {
            switch (data.action) {
                case 'detectAndHighlightFields':
                    console.log('🔍 بدء كشف وتمييز الحقول');
                    this.detectAndHighlightAllFields();
                    break;
                case 'testConnection':
                    console.log('🔧 رسالة اختبار الاتصال مستلمة');
                    this.sendTestResponse();
                    break;
                case 'activateFieldSelection':
                    console.log('🎯 تفعيل تحديد الحقول مع البيانات:', data.columns);
                    this.activate(data.columns, data.sampleData);
                    break;
                case 'deactivateFieldSelection':
                    console.log('⏹️ إيقاف تحديد الحقول');
                    this.deactivate();
                    break;
                case 'confirmFieldMapping':
                    console.log('✅ تأكيد ربط الحقل:', data.mapping);
                    this.confirmFieldMapping(data.mapping);
                    break;
                case 'cancelFieldMapping':
                    console.log('❌ إلغاء ربط الحقل');
                    this.cancelFieldMapping();
                    break;
                default:
                    console.log('❓ رسالة غير معروفة:', data.action);
            }
        } catch (error) {
            console.error('❌ خطأ في معالجة الرسالة:', error);
        }
    }

    sendTestResponse() {
        console.log('📤 إرسال رد اختبار الاتصال');

        window.parent.postMessage({
            source: 'field-selector',
            action: 'testResponse',
            status: 'success',
            timestamp: Date.now(),
            url: window.location.href
        }, '*');
    }

    detectAndHighlightAllFields() {
        console.log('🔍 بدء كشف جميع العناصر التفاعلية (مرئية ومخفية)...');

        // إزالة أي تمييز سابق
        this.clearAllHighlights();

        // كشف العناصر المختلفة (مرئية ومخفية)
        const inputs = this.detectInputFields();
        const buttons = this.detectButtons();
        const selects = this.detectSelectFields();
        const textareas = this.detectTextareas();

        // كشف العناصر المخفية أيضاً
        const hiddenInputs = this.detectHiddenInputFields();
        const hiddenButtons = this.detectHiddenButtons();
        const hiddenSelects = this.detectHiddenSelectFields();
        const hiddenTextareas = this.detectHiddenTextareas();

        // تطبيق التمييز الملون للعناصر المرئية
        this.highlightElements(inputs, '#4CAF50', '📝'); // أخضر للحقول
        this.highlightElements(buttons, '#2196F3', '🔘'); // أزرق للأزرار
        this.highlightElements(selects, '#FF9800', '📋'); // برتقالي للقوائم
        this.highlightElements(textareas, '#9C27B0', '📄'); // بنفسجي لمناطق النص

        // تطبيق تمييز مختلف للعناصر المخفية
        this.highlightHiddenElements(hiddenInputs, '#4CAF50', '📝🔒'); // أخضر مع قفل
        this.highlightHiddenElements(hiddenButtons, '#2196F3', '🔘🔒'); // أزرق مع قفل
        this.highlightHiddenElements(hiddenSelects, '#FF9800', '📋🔒'); // برتقالي مع قفل
        this.highlightHiddenElements(hiddenTextareas, '#9C27B0', '📄🔒'); // بنفسجي مع قفل

        // إرسال النتائج
        const results = {
            visible: {
                inputs: inputs.length,
                buttons: buttons.length,
                selects: selects.length,
                textareas: textareas.length,
                total: inputs.length + buttons.length + selects.length + textareas.length
            },
            hidden: {
                inputs: hiddenInputs.length,
                buttons: hiddenButtons.length,
                selects: hiddenSelects.length,
                textareas: hiddenTextareas.length,
                total: hiddenInputs.length + hiddenButtons.length + hiddenSelects.length + hiddenTextareas.length
            },
            grandTotal: inputs.length + buttons.length + selects.length + textareas.length +
                       hiddenInputs.length + hiddenButtons.length + hiddenSelects.length + hiddenTextareas.length
        };

        console.log('📊 نتائج الكشف:', results);

        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldsDetected',
            results: results
        }, '*');

        // إضافة أزرار التحكم
        this.addControlButtons();
    }

    detectInputFields() {
        const selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="number"]',
            'input[type="tel"]',
            'input[type="url"]',
            'input[type="search"]',
            'input[type="date"]',
            'input[type="time"]',
            'input[type="datetime-local"]',
            'input:not([type])'
        ];

        const inputs = [];
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (this.isElementVisible(element)) {
                    inputs.push(element);
                }
            });
        });

        console.log(`📝 تم العثور على ${inputs.length} حقل إدخال`);
        return inputs;
    }

    detectButtons() {
        const selectors = [
            'button',
            'input[type="button"]',
            'input[type="submit"]',
            'input[type="reset"]',
            '[role="button"]',
            'a[onclick]'
        ];

        const buttons = [];
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (this.isElementVisible(element)) {
                    buttons.push(element);
                }
            });
        });

        console.log(`🔘 تم العثور على ${buttons.length} زر`);
        return buttons;
    }

    detectSelectFields() {
        const selects = Array.from(document.querySelectorAll('select')).filter(element =>
            this.isElementVisible(element)
        );

        console.log(`📋 تم العثور على ${selects.length} قائمة منسدلة`);
        return selects;
    }

    detectTextareas() {
        const textareas = Array.from(document.querySelectorAll('textarea')).filter(element =>
            this.isElementVisible(element)
        );

        console.log(`📄 تم العثور على ${textareas.length} منطقة نص`);
        return textareas;
    }

    detectHiddenInputFields() {
        const selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="number"]',
            'input[type="tel"]',
            'input[type="url"]',
            'input[type="search"]',
            'input[type="date"]',
            'input[type="time"]',
            'input[type="datetime-local"]',
            'input:not([type])'
        ];

        const hiddenInputs = [];
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (!this.isElementVisible(element) && this.isElementInDOM(element)) {
                    hiddenInputs.push(element);
                }
            });
        });

        console.log(`📝🔒 تم العثور على ${hiddenInputs.length} حقل إدخال مخفي`);
        return hiddenInputs;
    }

    detectHiddenButtons() {
        const selectors = [
            'button',
            'input[type="button"]',
            'input[type="submit"]',
            'input[type="reset"]',
            '[role="button"]',
            'a[onclick]'
        ];

        const hiddenButtons = [];
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (!this.isElementVisible(element) && this.isElementInDOM(element)) {
                    hiddenButtons.push(element);
                }
            });
        });

        console.log(`🔘🔒 تم العثور على ${hiddenButtons.length} زر مخفي`);
        return hiddenButtons;
    }

    detectHiddenSelectFields() {
        const hiddenSelects = Array.from(document.querySelectorAll('select')).filter(element =>
            !this.isElementVisible(element) && this.isElementInDOM(element)
        );

        console.log(`📋🔒 تم العثور على ${hiddenSelects.length} قائمة منسدلة مخفية`);
        return hiddenSelects;
    }

    detectHiddenTextareas() {
        const hiddenTextareas = Array.from(document.querySelectorAll('textarea')).filter(element =>
            !this.isElementVisible(element) && this.isElementInDOM(element)
        );

        console.log(`📄🔒 تم العثور على ${hiddenTextareas.length} منطقة نص مخفية`);
        return hiddenTextareas;
    }

    isElementInDOM(element) {
        return document.contains(element);
    }

    isElementVisible(element) {
        const style = window.getComputedStyle(element);
        return element.offsetParent !== null &&
               style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               style.opacity !== '0' &&
               element.offsetWidth > 0 &&
               element.offsetHeight > 0;
    }

    highlightElements(elements, color, icon) {
        elements.forEach((element, index) => {
            this.highlightElement(element, color, icon, index + 1);
        });
    }

    highlightHiddenElements(elements, color, icon) {
        elements.forEach((element, index) => {
            this.highlightHiddenElement(element, color, icon, index + 1);
        });
    }

    highlightHiddenElement(element, color, icon, number) {
        // إنشاء عنصر بديل للعنصر المخفي
        const placeholder = document.createElement('div');
        placeholder.className = 'hidden-field-placeholder';
        placeholder.style.cssText = `
            position: relative;
            display: inline-block;
            margin: 5px;
            padding: 10px 15px;
            background: linear-gradient(135deg, ${color}20, ${color}40);
            border: 2px dashed ${color};
            border-radius: 8px;
            font-size: 12px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            direction: rtl;
            min-width: 150px;
        `;

        const fieldName = this.getFieldName(element);
        const fieldType = element.tagName.toLowerCase();
        const fieldId = element.id || element.name || 'بدون معرف';

        placeholder.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">
                ${icon} ${fieldName}
            </div>
            <div style="font-size: 10px; color: #666;">
                النوع: ${fieldType} | المعرف: ${fieldId}
            </div>
            <div style="font-size: 10px; color: #999; margin-top: 3px;">
                عنصر مخفي - انقر لإظهاره
            </div>
        `;

        // إضافة رقم
        const badge = document.createElement('div');
        badge.style.cssText = `
            position: absolute;
            top: -8px;
            right: -8px;
            background: ${color};
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            z-index: 10000;
        `;
        badge.textContent = number;
        placeholder.appendChild(badge);

        // إضافة تأثيرات التحويم
        placeholder.addEventListener('mouseenter', () => {
            placeholder.style.transform = 'scale(1.05)';
            placeholder.style.boxShadow = `0 4px 15px ${color}40`;
        });

        placeholder.addEventListener('mouseleave', () => {
            placeholder.style.transform = 'scale(1)';
            placeholder.style.boxShadow = 'none';
        });

        // إضافة وظيفة إظهار العنصر المخفي
        placeholder.addEventListener('click', () => {
            this.showHiddenElement(element, placeholder);
        });

        // إدراج العنصر البديل في مكان مناسب
        this.insertPlaceholder(element, placeholder);
    }

    insertPlaceholder(hiddenElement, placeholder) {
        // البحث عن مكان مناسب لإدراج العنصر البديل
        let insertionPoint = document.body;

        // محاولة العثور على الحاوي الأب
        let parent = hiddenElement.parentNode;
        while (parent && parent !== document.body) {
            const style = window.getComputedStyle(parent);
            if (style.display !== 'none' && style.visibility !== 'hidden') {
                insertionPoint = parent;
                break;
            }
            parent = parent.parentNode;
        }

        // إنشاء حاوي للعناصر المخفية إذا لم يكن موجوداً
        let hiddenContainer = document.getElementById('hidden-fields-container');
        if (!hiddenContainer) {
            hiddenContainer = document.createElement('div');
            hiddenContainer.id = 'hidden-fields-container';
            hiddenContainer.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                max-width: 300px;
                max-height: 400px;
                overflow-y: auto;
                background: rgba(255, 255, 255, 0.95);
                border: 2px solid #ddd;
                border-radius: 12px;
                padding: 15px;
                z-index: 10001;
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                direction: rtl;
            `;

            const header = document.createElement('div');
            header.style.cssText = `
                font-weight: bold;
                margin-bottom: 10px;
                padding-bottom: 8px;
                border-bottom: 1px solid #ddd;
                color: #333;
            `;
            header.textContent = '🔒 العناصر المخفية';

            hiddenContainer.appendChild(header);
            document.body.appendChild(hiddenContainer);
        }

        hiddenContainer.appendChild(placeholder);
    }

    showHiddenElement(element, placeholder) {
        console.log('👁️ إظهار العنصر المخفي:', element);

        // إظهار العنصر مؤقتاً
        const originalStyle = {
            display: element.style.display,
            visibility: element.style.visibility,
            opacity: element.style.opacity,
            position: element.style.position,
            zIndex: element.style.zIndex
        };

        element.style.display = 'block';
        element.style.visibility = 'visible';
        element.style.opacity = '1';
        element.style.position = 'relative';
        element.style.zIndex = '10000';
        element.style.outline = '3px solid #ff4444';
        element.style.backgroundColor = '#ffeeee';

        // إضافة رسالة تأكيد
        const confirmMsg = document.createElement('div');
        confirmMsg.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #ff4444;
            border-radius: 12px;
            padding: 20px;
            z-index: 10002;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            direction: rtl;
            text-align: center;
        `;

        confirmMsg.innerHTML = `
            <div style="margin-bottom: 15px; font-weight: bold;">
                👁️ تم إظهار العنصر المخفي مؤقتاً
            </div>
            <div style="margin-bottom: 15px; font-size: 14px; color: #666;">
                ${this.getFieldName(element)}
            </div>
            <button id="keep-visible" style="background: #4CAF50; color: white; border: none; padding: 8px 15px; border-radius: 6px; margin: 5px; cursor: pointer;">إبقاء مرئي</button>
            <button id="hide-again" style="background: #f44336; color: white; border: none; padding: 8px 15px; border-radius: 6px; margin: 5px; cursor: pointer;">إخفاء مرة أخرى</button>
        `;

        document.body.appendChild(confirmMsg);

        // معالجات الأزرار
        confirmMsg.querySelector('#keep-visible').addEventListener('click', () => {
            confirmMsg.remove();
            placeholder.remove();
        });

        confirmMsg.querySelector('#hide-again').addEventListener('click', () => {
            // إعادة الإعدادات الأصلية
            element.style.display = originalStyle.display;
            element.style.visibility = originalStyle.visibility;
            element.style.opacity = originalStyle.opacity;
            element.style.position = originalStyle.position;
            element.style.zIndex = originalStyle.zIndex;
            element.style.outline = '';
            element.style.backgroundColor = '';

            confirmMsg.remove();
        });
    }

    highlightElement(element, color, icon, number) {
        // إضافة إطار ملون
        element.style.outline = `3px solid ${color}`;
        element.style.outlineOffset = '2px';
        element.style.backgroundColor = `${color}15`; // شفافية 15%
        element.style.transition = 'all 0.3s ease';

        // إضافة رقم وأيقونة
        const badge = document.createElement('div');
        badge.className = 'field-highlight-badge';
        badge.style.cssText = `
            position: absolute;
            top: -10px;
            right: -10px;
            background: ${color};
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            animation: bounceIn 0.5s ease;
        `;
        badge.textContent = number;

        // جعل العنصر الأب relative إذا لم يكن كذلك
        const parent = element.parentNode;
        const originalPosition = window.getComputedStyle(parent).position;
        if (originalPosition === 'static') {
            parent.style.position = 'relative';
        }

        parent.appendChild(badge);

        // إضافة tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'field-highlight-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 10001;
            opacity: 0;
            transition: opacity 0.3s ease;
            direction: rtl;
        `;

        const fieldName = this.getFieldName(element);
        const fieldType = element.tagName.toLowerCase();
        tooltip.textContent = `${icon} ${fieldName} (${fieldType})`;

        parent.appendChild(tooltip);

        // إظهار tooltip عند التحويم
        element.addEventListener('mouseenter', () => {
            tooltip.style.opacity = '1';
        });

        element.addEventListener('mouseleave', () => {
            tooltip.style.opacity = '0';
        });
    }

    addControlButtons() {
        this.addClearHighlightsButton();
        this.addShowAllHiddenButton();
    }

    addShowAllHiddenButton() {
        // إزالة أي زر موجود
        const existingButton = document.getElementById('show-all-hidden-btn');
        if (existingButton) {
            existingButton.remove();
        }

        const showButton = document.createElement('button');
        showButton.id = 'show-all-hidden-btn';
        showButton.style.cssText = `
            position: fixed;
            top: 70px;
            right: 20px;
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10002;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
            transition: all 0.3s ease;
            direction: rtl;
        `;
        showButton.textContent = '👁️ إظهار جميع المخفي';

        showButton.addEventListener('click', () => {
            this.showAllHiddenElements();
        });

        showButton.addEventListener('mouseenter', () => {
            showButton.style.transform = 'scale(1.05)';
            showButton.style.boxShadow = '0 6px 20px rgba(255, 152, 0, 0.4)';
        });

        showButton.addEventListener('mouseleave', () => {
            showButton.style.transform = 'scale(1)';
            showButton.style.boxShadow = '0 4px 15px rgba(255, 152, 0, 0.3)';
        });

        document.body.appendChild(showButton);
    }

    showAllHiddenElements() {
        console.log('👁️ إظهار جميع العناصر المخفية...');

        const hiddenElements = document.querySelectorAll('input, button, select, textarea');
        let shownCount = 0;

        hiddenElements.forEach(element => {
            if (!this.isElementVisible(element) && this.isElementInDOM(element)) {
                element.style.display = 'block';
                element.style.visibility = 'visible';
                element.style.opacity = '1';
                element.style.outline = '2px solid #FF9800';
                element.style.backgroundColor = '#FFF3E0';
                shownCount++;
            }
        });

        // إزالة حاوي العناصر المخفية
        const hiddenContainer = document.getElementById('hidden-fields-container');
        if (hiddenContainer) {
            hiddenContainer.remove();
        }

        // إظهار رسالة تأكيد
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #FF9800;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 10003;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
            direction: rtl;
        `;
        notification.textContent = `👁️ تم إظهار ${shownCount} عنصر مخفي`;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    addClearHighlightsButton() {
        // إزالة أي زر موجود
        const existingButton = document.getElementById('clear-highlights-btn');
        if (existingButton) {
            existingButton.remove();
        }

        const clearButton = document.createElement('button');
        clearButton.id = 'clear-highlights-btn';
        clearButton.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            z-index: 10002;
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
            transition: all 0.3s ease;
            direction: rtl;
        `;
        clearButton.textContent = '🧹 إزالة التمييز';

        clearButton.addEventListener('click', () => {
            this.clearAllHighlights();
            clearButton.remove();
        });

        clearButton.addEventListener('mouseenter', () => {
            clearButton.style.transform = 'scale(1.05)';
            clearButton.style.boxShadow = '0 6px 20px rgba(244, 67, 54, 0.4)';
        });

        clearButton.addEventListener('mouseleave', () => {
            clearButton.style.transform = 'scale(1)';
            clearButton.style.boxShadow = '0 4px 15px rgba(244, 67, 54, 0.3)';
        });

        document.body.appendChild(clearButton);
    }

    clearAllHighlights() {
        console.log('🧹 إزالة جميع التمييزات...');

        // إزالة التمييز من العناصر
        const highlightedElements = document.querySelectorAll('[style*="outline"]');
        highlightedElements.forEach(element => {
            element.style.outline = '';
            element.style.outlineOffset = '';
            element.style.backgroundColor = '';
            element.style.transition = '';
        });

        // إزالة الشارات والتلميحات
        const badges = document.querySelectorAll('.field-highlight-badge');
        badges.forEach(badge => badge.remove());

        const tooltips = document.querySelectorAll('.field-highlight-tooltip');
        tooltips.forEach(tooltip => tooltip.remove());

        // إزالة العناصر البديلة للمخفية
        const placeholders = document.querySelectorAll('.hidden-field-placeholder');
        placeholders.forEach(placeholder => placeholder.remove());

        // إزالة حاوي العناصر المخفية
        const hiddenContainer = document.getElementById('hidden-fields-container');
        if (hiddenContainer) {
            hiddenContainer.remove();
        }

        // إزالة أزرار التحكم
        const clearButton = document.getElementById('clear-highlights-btn');
        if (clearButton) {
            clearButton.remove();
        }

        const showButton = document.getElementById('show-all-hidden-btn');
        if (showButton) {
            showButton.remove();
        }

        // إضافة أنيميشن CSS
        if (!document.getElementById('highlight-animations')) {
            const style = document.createElement('style');
            style.id = 'highlight-animations';
            style.textContent = `
                @keyframes bounceIn {
                    0% { transform: scale(0); opacity: 0; }
                    50% { transform: scale(1.2); opacity: 0.8; }
                    100% { transform: scale(1); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
    }

    activate(columns, sampleData) {
        console.log('🎯 تفعيل وضع تحديد الحقول');
        console.log('📊 الأعمدة المتاحة:', columns);
        console.log('📋 عينة البيانات:', sampleData);

        this.isActive = true;
        this.columns = columns || [];
        this.sampleData = sampleData || {};

        console.log('🎨 إنشاء الطبقة...');
        this.createOverlay();

        console.log('🖱️ إضافة مستمعات الأحداث...');
        this.attachEventListeners();

        console.log('💡 عرض التعليمات...');
        this.showInstructions();

        console.log('✅ تم تفعيل وضع التحديد بنجاح');
    }

    deactivate() {
        console.log('⏹️ إيقاف وضع تحديد الحقول');
        
        this.isActive = false;
        this.removeOverlay();
        this.removeEventListeners();
        this.clearHighlight();
    }

    createOverlay() {
        // إنشاء طبقة شفافة لعرض التعليمات
        this.overlay = document.createElement('div');
        this.overlay.id = 'field-selector-overlay';
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 123, 255, 0.1);
            z-index: 9999;
            pointer-events: none;
            direction: rtl;
            font-family: Arial, sans-serif;
        `;

        document.body.appendChild(this.overlay);
    }

    removeOverlay() {
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
    }

    showInstructions() {
        const instructions = document.createElement('div');
        instructions.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10001;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            animation: fadeInDown 0.5s ease;
            direction: rtl;
        `;

        instructions.innerHTML = `
            🖱️ انقر بالزر الأيمن على الحقول لربطها
            <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">
                ${this.columns.length} عمود متاح للربط • النقر الأيمن = قائمة الأعمدة
            </div>
        `;

        // إضافة الأنيميشن
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInDown {
                from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                to { opacity: 1; transform: translateX(-50%) translateY(0); }
            }
        `;
        document.head.appendChild(style);

        this.overlay.appendChild(instructions);

        // إزالة التعليمات بعد 5 ثوان
        setTimeout(() => {
            if (instructions.parentNode) {
                instructions.style.animation = 'fadeInDown 0.5s ease reverse';
                setTimeout(() => instructions.remove(), 500);
            }
        }, 5000);
    }

    attachEventListeners() {
        this.clickHandler = (e) => this.handleFieldClick(e);
        this.hoverHandler = (e) => this.handleFieldHover(e);
        this.leaveHandler = (e) => this.handleFieldLeave(e);
        this.contextMenuHandler = (e) => this.handleRightClick(e);

        document.addEventListener('click', this.clickHandler, true);
        document.addEventListener('mouseover', this.hoverHandler, true);
        document.addEventListener('mouseout', this.leaveHandler, true);
        document.addEventListener('contextmenu', this.contextMenuHandler, true);
    }

    removeEventListeners() {
        if (this.clickHandler) {
            document.removeEventListener('click', this.clickHandler, true);
            document.removeEventListener('mouseover', this.hoverHandler, true);
            document.removeEventListener('mouseout', this.leaveHandler, true);
            document.removeEventListener('contextmenu', this.contextMenuHandler, true);
        }
    }

    handleFieldClick(e) {
        if (!this.isActive) return;

        const element = e.target;
        
        // التحقق من أن العنصر حقل قابل للتعبئة
        if (!this.isFormField(element)) return;

        e.preventDefault();
        e.stopPropagation();

        console.log('🎯 تم النقر على حقل:', element);

        const field = this.extractFieldInfo(element);
        this.sendFieldSelected(field);
    }

    handleFieldHover(e) {
        if (!this.isActive) return;

        const element = e.target;
        
        if (this.isFormField(element)) {
            this.highlightElement(element);
        }
    }

    handleFieldLeave(e) {
        if (!this.isActive) return;
        this.clearHighlight();
    }

    handleRightClick(e) {
        console.log('🖱️ نقر أيمن مكتشف على:', e.target.tagName, e.target.type);

        if (!this.isActive) {
            console.log('❌ وضع التحديد غير مفعل');
            return;
        }

        const element = e.target;

        // التحقق من أن العنصر حقل قابل للتعبئة
        const isFormField = this.isFormField(element);
        console.log('🔍 هل هو حقل نموذج؟', isFormField);

        if (!isFormField) {
            console.log('❌ ليس حقل نموذج صالح');
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        console.log('✅ نقر أيمن صحيح على حقل:', element);
        console.log('📊 الأعمدة المتاحة:', this.columns.length);

        // إخفاء أي قائمة موجودة
        this.hideContextMenu();

        // إنشاء وإظهار قائمة الأعمدة
        this.showColumnContextMenu(e, element);
    }

    showColumnContextMenu(event, element) {
        const field = this.extractFieldInfo(element);

        // إنشاء قائمة السياق
        const contextMenu = document.createElement('div');
        contextMenu.id = 'column-context-menu';
        contextMenu.style.cssText = `
            position: fixed;
            background: white;
            border: 2px solid #007bff;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10003;
            min-width: 250px;
            max-height: 400px;
            overflow-y: auto;
            direction: rtl;
            font-family: Arial, sans-serif;
        `;

        // رأس القائمة
        const header = document.createElement('div');
        header.style.cssText = `
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 14px;
            border-radius: 6px 6px 0 0;
        `;
        header.innerHTML = `🎯 ربط الحقل: ${field.name}`;

        contextMenu.appendChild(header);

        // قائمة الأعمدة
        const columnsList = document.createElement('div');
        columnsList.style.cssText = `
            padding: 8px 0;
        `;

        // إضافة خيار "بدون ربط"
        const noMappingOption = this.createColumnOption('', 'بدون ربط', '❌', element, field);
        columnsList.appendChild(noMappingOption);

        // إضافة خط فاصل
        const separator = document.createElement('div');
        separator.style.cssText = `
            height: 1px;
            background: #dee2e6;
            margin: 5px 0;
        `;
        columnsList.appendChild(separator);

        // إضافة الأعمدة
        this.columns.forEach(column => {
            const sampleData = this.getSampleDataForColumn(column);
            const option = this.createColumnOption(column, column, '📊', element, field, sampleData);
            columnsList.appendChild(option);
        });

        // إضافة خط فاصل
        const separator2 = document.createElement('div');
        separator2.style.cssText = `
            height: 1px;
            background: #dee2e6;
            margin: 5px 0;
        `;
        columnsList.appendChild(separator2);

        // إضافة خيار "قيمة مخصصة"
        const customOption = this.createCustomValueOption(element, field);
        columnsList.appendChild(customOption);

        contextMenu.appendChild(columnsList);

        // تحديد موقع القائمة
        const x = event.clientX;
        const y = event.clientY;

        contextMenu.style.left = x + 'px';
        contextMenu.style.top = y + 'px';

        document.body.appendChild(contextMenu);

        // إضافة مستمع لإغلاق القائمة عند النقر خارجها
        setTimeout(() => {
            document.addEventListener('click', this.hideContextMenuHandler = () => {
                this.hideContextMenu();
            });
        }, 100);
    }

    createColumnOption(columnValue, displayText, icon, element, field, sampleData = null) {
        const option = document.createElement('div');
        option.style.cssText = `
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        option.innerHTML = `
            <span style="font-size: 16px;">${icon}</span>
            <div style="flex: 1;">
                <div style="font-weight: bold; color: #333;">${displayText}</div>
                ${sampleData ? `<div style="font-size: 12px; color: #666; margin-top: 2px;">مثال: ${sampleData}</div>` : ''}
            </div>
        `;

        // تأثيرات التحويم
        option.addEventListener('mouseenter', () => {
            option.style.backgroundColor = '#e3f2fd';
        });

        option.addEventListener('mouseleave', () => {
            option.style.backgroundColor = '';
        });

        // معالج النقر
        option.addEventListener('click', () => {
            this.selectColumn(columnValue, element, field);
        });

        return option;
    }

    createCustomValueOption(element, field) {
        const option = document.createElement('div');
        option.style.cssText = `
            padding: 10px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        `;

        option.innerHTML = `
            <span style="font-size: 16px;">✏️</span>
            <div style="flex: 1;">
                <div style="font-weight: bold; color: #333;">قيمة مخصصة</div>
                <div style="font-size: 12px; color: #666; margin-top: 2px;">أدخل قيمة ثابتة</div>
            </div>
        `;

        // تأثيرات التحويم
        option.addEventListener('mouseenter', () => {
            option.style.backgroundColor = '#fff3cd';
        });

        option.addEventListener('mouseleave', () => {
            option.style.backgroundColor = '';
        });

        // معالج النقر
        option.addEventListener('click', () => {
            this.showCustomValueInput(element, field);
        });

        return option;
    }

    selectColumn(columnValue, element, field) {
        console.log('📊 تم اختيار العمود:', columnValue, 'للحقل:', field.name);

        this.hideContextMenu();

        if (!columnValue) {
            // إزالة الربط
            this.removeFieldMapping(element, field);
            return;
        }

        // إنشاء الربط
        const mapping = {
            selector: field.selector,
            column: columnValue,
            customValue: null,
            type: field.type,
            fieldName: field.name
        };

        // تطبيق الربط
        this.confirmFieldMapping(mapping);
    }

    showCustomValueInput(element, field) {
        this.hideContextMenu();

        // إنشاء نافذة إدخال القيمة المخصصة
        const inputDialog = document.createElement('div');
        inputDialog.id = 'custom-value-dialog';
        inputDialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #ffc107;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            z-index: 10004;
            min-width: 300px;
            direction: rtl;
            font-family: Arial, sans-serif;
        `;

        inputDialog.innerHTML = `
            <div style="background: linear-gradient(135deg, #ffc107, #e0a800); color: #212529; padding: 15px; border-radius: 10px 10px 0 0;">
                <h3 style="margin: 0; font-size: 16px;">✏️ قيمة مخصصة</h3>
                <div style="font-size: 14px; margin-top: 5px; opacity: 0.8;">${field.name}</div>
            </div>

            <div style="padding: 20px;">
                <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">أدخل القيمة:</label>
                <input type="text" id="custom-value-input" placeholder="القيمة المطلوبة..." style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 6px; font-size: 14px; direction: rtl;">

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 15px;">
                    <button id="confirm-custom-value" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px;">✅ تأكيد</button>
                    <button id="cancel-custom-value" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; font-size: 14px;">❌ إلغاء</button>
                </div>
            </div>
        `;

        document.body.appendChild(inputDialog);

        // التركيز على حقل الإدخال
        const input = inputDialog.querySelector('#custom-value-input');
        input.focus();

        // معالجات الأحداث
        inputDialog.querySelector('#confirm-custom-value').addEventListener('click', () => {
            const customValue = input.value.trim();
            if (!customValue) {
                alert('يرجى إدخال قيمة');
                return;
            }

            const mapping = {
                selector: field.selector,
                column: null,
                customValue: customValue,
                type: field.type,
                fieldName: field.name
            };

            this.confirmFieldMapping(mapping);
            inputDialog.remove();
        });

        inputDialog.querySelector('#cancel-custom-value').addEventListener('click', () => {
            inputDialog.remove();
        });

        // إدخال Enter للتأكيد
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                inputDialog.querySelector('#confirm-custom-value').click();
            }
        });
    }

    getSampleDataForColumn(columnName) {
        if (!this.sampleData || !this.sampleData[columnName]) return null;

        const sampleValue = this.sampleData[columnName];
        if (!sampleValue) return null;

        // اقتطاع النص إذا كان طويلاً
        const maxLength = 25;
        if (sampleValue.length > maxLength) {
            return sampleValue.substring(0, maxLength) + '...';
        }

        return sampleValue;
    }

    hideContextMenu() {
        const contextMenu = document.getElementById('column-context-menu');
        if (contextMenu) {
            contextMenu.remove();
        }

        const customDialog = document.getElementById('custom-value-dialog');
        if (customDialog) {
            customDialog.remove();
        }

        // إزالة مستمع إغلاق القائمة
        if (this.hideContextMenuHandler) {
            document.removeEventListener('click', this.hideContextMenuHandler);
            this.hideContextMenuHandler = null;
        }
    }

    removeFieldMapping(element, field) {
        console.log('❌ إزالة ربط الحقل:', field.name);

        // إزالة التمييز
        element.style.border = '';
        element.style.backgroundColor = '';

        // إزالة أيقونة التأكيد
        const icon = element.parentNode.querySelector('span');
        if (icon && icon.textContent === '✓') {
            icon.remove();
        }

        // إزالة من الحقول المحددة
        delete this.selectedFields[field.selector];

        // إرسال رسالة الإزالة
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldRemoved',
            field: field
        }, '*');
    }

    isFormField(element) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type ? element.type.toLowerCase() : '';

        console.log(`🔍 فحص العنصر: ${tagName}, النوع: ${type}`);

        const validTags = ['input', 'textarea', 'select'];
        const validTypes = ['text', 'email', 'number', 'tel', 'url', 'password', 'search', ''];

        // التحقق من أن العنصر مرئي
        const isVisible = element.offsetParent !== null &&
                          element.style.display !== 'none' &&
                          element.style.visibility !== 'hidden';

        console.log(`👁️ مرئي: ${isVisible}`);

        if (tagName === 'input') {
            const isValidInput = validTypes.includes(type);
            console.log(`✅ input صالح: ${isValidInput}`);
            return isValidInput && isVisible;
        }

        const isValidTag = validTags.includes(tagName);
        console.log(`✅ tag صالح: ${isValidTag}`);

        return isValidTag && isVisible;
    }

    extractFieldInfo(element) {
        const field = {
            selector: this.generateElementSelector(element),
            type: element.type || element.tagName.toLowerCase(),
            name: this.getFieldName(element),
            placeholder: element.placeholder || '',
            value: element.value || '',
            required: element.required || false
        };

        // إضافة خيارات القائمة المنسدلة
        if (element.tagName.toLowerCase() === 'select') {
            field.options = Array.from(element.options).map(option => ({
                value: option.value,
                text: option.textContent.trim()
            }));
        }

        return field;
    }

    getFieldName(element) {
        // محاولة الحصول على اسم الحقل من مصادر مختلفة
        if (element.name) return element.name;
        if (element.id) return element.id;
        if (element.placeholder) return element.placeholder;
        
        // البحث في التسميات المرتبطة
        const label = this.findAssociatedLabel(element);
        if (label) return label.textContent.trim();

        // البحث في النص المجاور
        const nearbyText = this.findNearbyText(element);
        if (nearbyText) return nearbyText;

        return `حقل ${element.tagName.toLowerCase()}`;
    }

    findAssociatedLabel(element) {
        // البحث عن label مرتبط بـ for
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) return label;
        }

        // البحث عن label يحتوي على العنصر
        let parent = element.parentNode;
        while (parent && parent.tagName) {
            if (parent.tagName.toLowerCase() === 'label') {
                return parent;
            }
            parent = parent.parentNode;
        }

        return null;
    }

    findNearbyText(element) {
        // البحث في العناصر المجاورة
        const siblings = Array.from(element.parentNode.children);
        const elementIndex = siblings.indexOf(element);

        // البحث في العنصر السابق
        for (let i = elementIndex - 1; i >= 0; i--) {
            const sibling = siblings[i];
            const text = sibling.textContent.trim();
            if (text && text.length < 50) {
                return text;
            }
        }

        return null;
    }

    generateElementSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }
        
        if (element.name) {
            return `[name="${element.name}"]`;
        }
        
        // إنشاء محدد CSS فريد
        let selector = element.tagName.toLowerCase();
        
        if (element.className) {
            const classes = element.className.split(' ').filter(cls => cls.trim());
            if (classes.length > 0) {
                selector += '.' + classes.join('.');
            }
        }
        
        // إضافة placeholder كمحدد إضافي
        if (element.placeholder) {
            selector += `[placeholder="${element.placeholder}"]`;
        }
        
        return selector;
    }

    highlightElement(element) {
        this.clearHighlight();
        
        this.highlightedElement = element;
        element.style.outline = '3px solid #007bff';
        element.style.outlineOffset = '2px';
        element.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
        
        // إضافة tooltip
        this.showTooltip(element);
    }

    clearHighlight() {
        if (this.highlightedElement) {
            this.highlightedElement.style.outline = '';
            this.highlightedElement.style.outlineOffset = '';
            this.highlightedElement.style.backgroundColor = '';
            this.highlightedElement = null;
        }
        
        this.hideTooltip();
    }

    showTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.id = 'field-selector-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10002;
            pointer-events: none;
            direction: rtl;
        `;
        
        const fieldName = this.getFieldName(element);
        tooltip.textContent = `نقر أيمن لربط: ${fieldName}`;
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + 'px';
        tooltip.style.top = (rect.top - 35) + 'px';
        
        document.body.appendChild(tooltip);
    }

    hideTooltip() {
        const tooltip = document.getElementById('field-selector-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    sendFieldSelected(field) {
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldSelected',
            field: field
        }, '*');
    }

    confirmFieldMapping(mapping) {
        console.log('✅ تأكيد ربط الحقل:', mapping);
        
        // حفظ الربط محلياً
        this.selectedFields[mapping.selector] = mapping;
        
        // إضافة علامة على الحقل المربوط
        const element = document.querySelector(mapping.selector);
        if (element) {
            this.markFieldAsMapped(element, mapping);
        }

        // إرسال تأكيد الربط
        window.parent.postMessage({
            source: 'field-selector',
            action: 'fieldMapped',
            mapping: mapping
        }, '*');
    }

    markFieldAsMapped(element, mapping) {
        element.style.border = '2px solid #28a745';
        element.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
        
        // إضافة أيقونة تأكيد
        const icon = document.createElement('span');
        icon.style.cssText = `
            position: absolute;
            right: -25px;
            top: 50%;
            transform: translateY(-50%);
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        `;
        icon.textContent = '✓';
        
        // جعل العنصر الأب relative إذا لم يكن كذلك
        const parent = element.parentNode;
        if (getComputedStyle(parent).position === 'static') {
            parent.style.position = 'relative';
        }
        
        parent.appendChild(icon);
    }

    cancelFieldMapping() {
        console.log('❌ إلغاء ربط الحقل');
        this.clearHighlight();
    }
}

// تشغيل Field Selector عند تحميل الصفحة
console.log('🔧 field-selector.js تم تحميله');

let fieldSelectorInstance = null;

function initializeFieldSelector() {
    if (!fieldSelectorInstance) {
        fieldSelectorInstance = new FieldSelector();
        console.log('✅ Field Selector تم تهيئته بنجاح');
    }
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeFieldSelector);
} else {
    initializeFieldSelector();
}

// إضافة مستمع إضافي للتأكد من التحميل
window.addEventListener('load', () => {
    if (!fieldSelectorInstance) {
        console.log('🔄 إعادة محاولة تهيئة Field Selector');
        initializeFieldSelector();
    }
});
